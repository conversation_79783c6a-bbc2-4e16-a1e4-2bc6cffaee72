# Fixed Setup Commands for RAG Testing

## Prerequisites
- User `<EMAIL>` already exists
- Django server should be stopped before running these commands
- Embedding consistency fix has been applied

## 1. Clean Databases

```bash
# Navigate to Django project
cd /Users/<USER>/Desktop/RAGSearch/multi_source_rag

# Clean PostgreSQL database
python manage.py flush --noinput

# Clean Qdrant vector database
python -c "
import qdrant_client
try:
    client = qdrant_client.QdrantClient(host='localhost', port=6333)
    collections = client.get_collections().collections
    for collection in collections:
        try:
            client.delete_collection(collection.name)
            print(f'✅ Deleted collection: {collection.name}')
        except Exception as e:
            print(f'⚠️  Error deleting {collection.name}: {e}')
    print('✅ Qdrant cleanup complete')
except Exception as e:
    print(f'❌ Qdrant cleanup failed: {e}')
    print('Make sure Qdrant is running on localhost:6333')
"
```

## 2. Run Database Migrations

```bash
# Apply all migrations
python manage.py migrate
```

## 3. Seed Metadata (Updated for your user)

```bash
# Seed tenant, sources, and update existing user
python manage.py shell -c "
from django.contrib.auth.models import User
from apps.accounts.models import Tenant, UserProfile
from apps.core.models import Source

# Create default tenant
tenant, created = Tenant.objects.get_or_create(
    slug='stride',
    defaults={
        'name': 'Stride Technologies',
        'is_active': True,
        'description': 'Default tenant for RAG system'
    }
)
print(f'✅ Tenant: {tenant.name} ({'created' if created else 'exists'})')

# Get existing user
try:
    user = User.objects.get(email='<EMAIL>')
    print(f'✅ Found existing user: {user.username} ({user.email})')
    
    # Ensure user is active
    if not user.is_active:
        user.is_active = True
        user.save()
        print('✅ Activated user account')
        
except User.DoesNotExist:
    print('❌ User <EMAIL> not found. Please create it first.')
    exit(1)

# Create or update user profile
profile, created = UserProfile.objects.get_or_create(
    user=user,
    defaults={'tenant': tenant}
)
if not created and profile.tenant != tenant:
    profile.tenant = tenant
    profile.save()
    print('✅ Updated user profile tenant')
else:
    print(f'✅ User Profile: ({'created' if created else 'exists'})')

# Create data sources
sources_data = [
    {
        'name': 'Slack Workspace',
        'source_type': 'slack',
        'description': 'Company Slack workspace channels',
        'is_active': True
    },
    {
        'name': 'GitHub Repositories',
        'source_type': 'github',
        'description': 'Code repositories and documentation',
        'is_active': True
    },
    {
        'name': 'Confluence Wiki',
        'source_type': 'confluence',
        'description': 'Company knowledge base and documentation',
        'is_active': True
    },
    {
        'name': 'Google Docs',
        'source_type': 'google_docs',
        'description': 'Shared documents and specifications',
        'is_active': True
    }
]

for source_data in sources_data:
    source, created = Source.objects.get_or_create(
        tenant=tenant,
        source_type=source_data['source_type'],
        defaults=source_data
    )
    print(f'✅ Source: {source.name} ({'created' if created else 'exists'})')

print('✅ Metadata seeding complete!')
"
```

## 4. Test Embedding Consistency (Optional but Recommended)

```bash
# Test embedding model initialization
python manage.py shell -c "
print('🔍 Testing embedding consistency...')
try:
    from apps.core.utils.embedding_consistency import get_consistent_embedding_model, get_embedding_model_info
    
    print('📊 Getting model info...')
    model_info = get_embedding_model_info()
    print(f'✅ Model Type: {model_info.get(\"model_type\", \"Unknown\")}')
    print(f'✅ Model Name: {model_info.get(\"model_name\", \"Unknown\")}')
    print(f'✅ Dimensions: {model_info.get(\"dimensions\", \"Unknown\")}')
    
    print('🚀 Initializing model (may take a moment to download)...')
    model = get_consistent_embedding_model()
    print(f'✅ Model initialized successfully: {type(model).__name__}')
    
    print('✅ Embedding consistency test passed!')
    
except Exception as e:
    print(f'❌ Embedding consistency test failed: {e}')
    print('This may indicate network issues or missing dependencies.')
"
```

## 5. Ingest Data from Data Folder

```bash
# Check available data files
ls -la data/slack/ 2>/dev/null || echo "⚠️  No data/slack folder found"

# Ingest Slack data using the LocalSlackSourceInterface
python manage.py shell -c "
print('🚀 Starting data ingestion...')
try:
    from apps.documents.services.ingestion_service import IngestionService
    from apps.documents.interfaces.local_slack_interface import LocalSlackSourceInterface
    from apps.core.models import Source
    from apps.accounts.models import Tenant
    import os

    # Get tenant and source
    tenant = Tenant.objects.get(slug='stride')
    slack_source = Source.objects.get(tenant=tenant, source_type='slack')

    print(f'✅ Using tenant: {tenant.name}')
    print(f'✅ Using source: {slack_source.name}')

    # Check if data folder exists
    data_folder = '/Users/<USER>/Desktop/RAGSearch/multi_source_rag/data/slack'
    if not os.path.exists(data_folder):
        print(f'❌ Data folder not found: {data_folder}')
        print('Please ensure Slack data is available in the data/slack folder')
        exit(1)

    # Initialize ingestion service
    print('📊 Initializing ingestion service...')
    ingestion_service = IngestionService(tenant_slug='stride')

    # Initialize local Slack interface
    print('📁 Initializing local Slack interface...')
    slack_interface = LocalSlackSourceInterface(data_folder=data_folder)

    print('⚡ Starting ingestion process...')
    result = ingestion_service.ingest_from_source(
        source=slack_source,
        source_interface=slack_interface,
        batch_size=50,
        clean_before_ingest=True
    )
    
    print('🎉 Ingestion completed successfully!')
    print(f'📊 Documents processed: {result.get(\"documents_processed\", \"N/A\")}')
    print(f'📊 Chunks created: {result.get(\"chunks_created\", \"N/A\")}')
    print(f'📊 Vectors indexed: {result.get(\"vectors_indexed\", \"N/A\")}')
    
except Exception as e:
    print(f'❌ Ingestion failed: {str(e)}')
    import traceback
    traceback.print_exc()
    print('')
    print('💡 Common solutions:')
    print('   - Ensure data/slack folder exists with JSON files')
    print('   - Check that Qdrant is running (docker ps)')
    print('   - Verify internet connection for embedding model download')
"
```

## 6. Verify Data Ingestion

```bash
# Check ingested data statistics
python manage.py shell -c "
from apps.core.models import Document, DocumentChunk
from apps.accounts.models import Tenant

tenant = Tenant.objects.get(slug='stride')

# Count documents and chunks
doc_count = Document.objects.filter(source__tenant=tenant).count()
chunk_count = DocumentChunk.objects.filter(document__source__tenant=tenant).count()

print(f'📊 === Data Ingestion Summary ===')
print(f'🏢 Tenant: {tenant.name}')
print(f'📄 Documents: {doc_count}')
print(f'🧩 Document Chunks: {chunk_count}')

if doc_count > 0:
    print(f'\\n📋 === Sample Documents ===')
    for doc in Document.objects.filter(source__tenant=tenant)[:5]:
        print(f'   📄 {doc.title[:60]}... (Source: {doc.source.source_type})')

# Check Qdrant collections
try:
    import qdrant_client
    client = qdrant_client.QdrantClient(host='localhost', port=6333)
    collections = client.get_collections().collections
    print(f'\\n🔍 === Qdrant Collections ===')
    for collection in collections:
        info = client.get_collection(collection.name)
        print(f'   🗂️  {collection.name}: {info.points_count} vectors')
except Exception as e:
    print(f'\\n❌ Qdrant check failed: {e}')
    print('   Make sure Qdrant is running: docker ps')
"
```

## 7. Start Django Server for Manual Testing

```bash
# Start the development server
python manage.py runserver

# Server will be available at: http://localhost:8000
# Login with your existing <NAME_EMAIL>
```

## 8. Test RAG Service (Optional)

```bash
# Test the RAG service initialization and search
python manage.py shell -c "
print('🧪 Testing RAG Service...')
try:
    from apps.search.services.rag_service import RAGService

    print('🚀 Initializing RAG Service...')
    rag_service = RAGService(tenant_slug='stride')
    print(f'✅ RAG Service initialized: {type(rag_service).__name__}')
    
    # Test a simple search
    print('🔍 Testing search functionality...')
    result, docs = rag_service.search(
        query_text='What are the main topics discussed?',
        top_k=3,
        min_relevance_score=0.1
    )
    print(f'✅ Search completed successfully')
    print(f'📊 Retrieved {len(docs)} documents')
    print(f'💬 Response length: {len(result) if result else 0} characters')
    
    if result:
        print(f'📝 Response preview: {result[:200]}...')
    
except Exception as e:
    print(f'❌ RAG Service test failed: {str(e)}')
    import traceback
    traceback.print_exc()
"
```

## Troubleshooting

### If embedding model download fails:
```bash
# Check internet connection and try again
# Or set up Gemini API key to use Gemini embeddings instead:
export GEMINI_API_KEY=your_api_key_here
export USE_GEMINI_EMBEDDING=true
```

### If Qdrant connection fails:
```bash
# Check if Qdrant is running
docker ps | grep qdrant

# Start Qdrant if not running
docker run -p 6333:6333 qdrant/qdrant
```

### If data folder is missing:
```bash
# Create sample data folder structure
mkdir -p data/slack
echo "Place your Slack export JSON files in data/slack/"
```

## Expected Results

After successful completion:
- ✅ Clean database with proper migrations
- ✅ Tenant and user properly configured
- ✅ Data sources created
- ✅ Consistent embedding model initialized
- ✅ Slack data ingested with proper chunking and vectorization
- ✅ RAG system ready for manual testing through UI

Login at http://localhost:8000 with your existing `<EMAIL>` credentials to test the search functionality!
