import os
import json
import hashlib
import logging
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Set
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path

from .base import DocumentSourceInterface

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class SlackMessage:
    """
    Data class representing a Slack message with comprehensive metadata.

    This class encapsulates all relevant information about a Slack message,
    including content, timing, authorship, and engagement metrics.

    Attributes:
        text: The message content
        user: User ID of the message sender
        timestamp: Unix timestamp of the message
        thread_ts: Thread timestamp (if message is part of a thread)
        channel: Channel ID where message was posted
        user_name: Human-readable username
        reactions: List of emoji reactions on the message
        reply_count: Number of replies in thread (if applicable)
        files: List of attached files
        edited: Whether the message was edited
        subtype: Message subtype (bot_message, file_share, etc.)
    """
    text: str
    user: str
    timestamp: str
    thread_ts: Optional[str] = None
    channel: str = ""
    user_name: str = ""
    reactions: List[str] = None
    reply_count: int = 0
    files: List[Dict] = None
    edited: bool = False
    subtype: Optional[str] = None

    def __post_init__(self):
        """Initialize default values for optional fields."""
        if self.reactions is None:
            self.reactions = []
        if self.files is None:
            self.files = []

    @property
    def readable_timestamp(self) -> str:
        """Convert timestamp to human-readable format."""
        return datetime.fromtimestamp(float(self.timestamp)).strftime("%Y-%m-%d %H:%M:%S")

    @property
    def is_thread_parent(self) -> bool:
        """Check if this message is the parent of a thread."""
        return self.thread_ts is None and self.reply_count > 0

    @property
    def is_thread_reply(self) -> bool:
        """Check if this message is a reply in a thread."""
        return self.thread_ts is not None


@dataclass
class StagedDataInfo:
    """Information about staged data availability."""
    channel_id: str
    data_dir: Path
    available_dates: List[str]
    thread_count: int
    user_count: int
    last_staged: Optional[str]
    total_messages: int


class LocalSlackSourceInterface(DocumentSourceInterface):
    """
    Enhanced Slack interface for processing local Slack JSON exports.
    Uses LlamaIndex-compatible processing to create meaningful documents with preserved conversation context.

    Features:
    - Time-based aggregation (monthly, weekly, daily, or custom periods)
    - Thread preservation within time periods
    - Quality assessment for documents
    - Rich metadata extraction
    - Optional summarization
    - Search functionality with relevance scoring
    - Semantic cross-references between related documents
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.data_dir = config.get("data_dir", "data/slack/")
        if not os.path.isabs(self.data_dir):
            self.data_dir = os.path.abspath(self.data_dir)

        # Channel ID to process (if specified)
        self.channel_id = config.get("channel", "C065QSSNH8A")

        # Set up directory paths
        self.base_data_dir = Path(self.data_dir)
        self.channel_dir = self._find_channel_directory()

        if self.channel_dir:
            self.messages_dir = self.channel_dir / "messages"
            self.threads_dir = self.channel_dir / "threads"
            self.users_dir = self.channel_dir / "users"
            self.metadata_dir = self.channel_dir / "metadata"

            # Load user cache and metadata
            self.user_cache = self._load_user_cache()
            self.metadata = self._load_metadata()
        else:
            # Initialize empty caches if no channel directory found
            self.user_cache = {}
            self.metadata = {}

        # Statistics tracking
        self.stats = {
            'messages_loaded': 0,
            'threads_loaded': 0,
            'files_processed': 0,
            'load_time_seconds': 0.0
        }

        # Pagination cursor for compatibility with the interface
        self.pagination_cursor = None

        # Document caching for pagination
        self._current_documents = None
        self._next_cursor = None
        self._document_cache = {}  # Changed to dict for better lookup

        # Configuration options
        self.max_gap_minutes = config.get("max_gap_minutes", 30)
        self.enable_summary = config.get("enable_summary", False)
        self.time_period = config.get("time_period", "monthly")  # Options: monthly, weekly, daily, custom
        self.custom_days = config.get("custom_days", 30)  # For custom time period
        self.quality_threshold = config.get("quality_threshold", 0.3)
        self.max_documents_per_channel = config.get("max_documents_per_channel", 10000)

        # Additional configuration options
        self.enable_semantic_cross_refs = config.get("enable_semantic_cross_refs", True)
        self.include_threads = config.get("include_threads", True)
        self.filter_bots = config.get("filter_bots", True)
        self.summarize_documents = config.get("summarize_documents", False)

        # Initialize embedding-related attributes
        self.use_embeddings = config.get("use_embeddings", False)
        self.embedding_content_type = config.get("embedding_content_type", "conversation")
        self.embedding_model_name = config.get("embedding_model_name", "bge-base-en-v1.5")
        self.embedding_cache_size = config.get("embedding_cache_size", 1000)
        self.embedding_batch_size = config.get("embedding_batch_size", 10)
        self._embedding_model = None
        self._embedding_cache = {}

        # Cross-reference configuration
        self.cross_ref_min_similarity = config.get("cross_ref_min_similarity", 0.3)
        self.cross_ref_max_references = config.get("cross_ref_max_references", 5)
        self.cross_ref_time_window_days = config.get("cross_ref_time_window_days", 30)
        self.bidirectional_references = config.get("bidirectional_references", True)
        self.progress_logging_interval = config.get("progress_logging_interval", 100)

    def _find_channel_directory(self) -> Optional[Path]:
        """Find the channel directory based on channel_id or auto-detect."""
        if self.channel_id:
            # Look for specific channel
            channel_dir = self.base_data_dir / f"channel_{self.channel_id}"
            if channel_dir.exists():
                return channel_dir
        else:
            # Auto-detect first available channel
            for item in self.base_data_dir.iterdir():
                if item.is_dir() and item.name.startswith("channel_"):
                    self.channel_id = item.name.replace("channel_", "")
                    return item

        return None

    def _load_user_cache(self) -> Dict[str, Dict[str, Any]]:
        """Load user cache from staged data."""
        if not hasattr(self, 'users_dir') or not self.users_dir:
            return {}

        users_file = self.users_dir / "users.json"

        if not users_file.exists():
            logger.warning("No user cache found")
            return {}

        try:
            with open(users_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('users', {})
        except Exception as e:
            logger.error(f"Failed to load user cache: {e}")
            return {}

    def _load_metadata(self) -> Dict[str, Any]:
        """Load staging metadata."""
        if not hasattr(self, 'metadata_dir') or not self.metadata_dir:
            return {}

        metadata_file = self.metadata_dir / "staging_metadata.json"

        if not metadata_file.exists():
            return {}

        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load metadata: {e}")
            return {}

    def get_staged_data_info(self) -> StagedDataInfo:
        """
        Get information about available staged data.

        Returns:
            StagedDataInfo object with data availability details
        """
        if not hasattr(self, 'messages_dir') or not self.messages_dir:
            return StagedDataInfo(
                channel_id=self.channel_id or "",
                data_dir=Path(self.data_dir),
                available_dates=[],
                thread_count=0,
                user_count=0,
                last_staged=None,
                total_messages=0
            )

        # Get available message dates
        available_dates = []
        total_messages = 0

        if self.messages_dir.exists():
            for message_file in self.messages_dir.glob("messages_*.json"):
                date_str = message_file.stem.replace("messages_", "")
                available_dates.append(date_str)

                # Count messages in this file
                try:
                    with open(message_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        total_messages += data.get('message_count', 0)
                except Exception:
                    pass

        # Count threads
        thread_count = 0
        if self.threads_dir.exists():
            thread_count = len(list(self.threads_dir.glob("thread_*.json")))

        # Get last staged time
        last_staged = None
        if self.metadata.get('staging_runs'):
            last_run = self.metadata['staging_runs'][-1]
            last_staged = last_run.get('end_time')

        return StagedDataInfo(
            channel_id=self.channel_id or "",
            data_dir=self.channel_dir or Path(self.data_dir),
            available_dates=sorted(available_dates),
            thread_count=thread_count,
            user_count=len(self.user_cache),
            last_staged=last_staged,
            total_messages=total_messages
        )

    def _determine_dates_to_load(self, days_back: int, date_filter: Optional[List[str]]) -> List[str]:
        """Determine which date files to load based on criteria."""
        if not hasattr(self, 'messages_dir') or not self.messages_dir:
            return []

        if date_filter:
            # Use specific date filter
            available_dates = self._get_available_dates()
            return [date for date in date_filter if date in available_dates]

        # Use days_back to filter
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date - timedelta(days=days_back)

        available_dates = []
        for message_file in self.messages_dir.glob("messages_*.json"):
            date_str = message_file.stem.replace("messages_", "")
            try:
                file_date = datetime.strptime(date_str, "%Y-%m-%d")
                if file_date >= cutoff_date:
                    available_dates.append(date_str)
            except ValueError:
                logger.warning(f"Invalid date format in filename: {message_file}")

        return sorted(available_dates)

    def _get_available_dates(self) -> Set[str]:
        """Get set of available message dates."""
        dates = set()
        if hasattr(self, 'messages_dir') and self.messages_dir and self.messages_dir.exists():
            for message_file in self.messages_dir.glob("messages_*.json"):
                date_str = message_file.stem.replace("messages_", "")
                dates.add(date_str)
        return dates

    def _load_messages_for_date(self, date_str: str, filter_bots: bool) -> List[SlackMessage]:
        """Load messages for a specific date."""
        if not hasattr(self, 'messages_dir') or not self.messages_dir:
            return []

        message_file = self.messages_dir / f"messages_{date_str}.json"

        if not message_file.exists():
            logger.warning(f"Message file not found: {message_file}")
            return []

        try:
            with open(message_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            messages = []
            for msg_data in data.get('messages', []):
                slack_msg = self._create_slack_message(msg_data, filter_bots)
                if slack_msg:
                    messages.append(slack_msg)

            logger.debug(f"Loaded {len(messages)} messages from {date_str}")
            return messages

        except Exception as e:
            logger.error(f"Failed to load messages from {message_file}: {e}")
            return []

    def _load_thread_messages(self, parent_messages: List[SlackMessage], filter_bots: bool) -> List[SlackMessage]:
        """Load thread replies for messages that have threads."""
        if not hasattr(self, 'threads_dir') or not self.threads_dir:
            return []

        thread_messages = []

        # Find parent messages that have thread replies
        thread_parents = [msg for msg in parent_messages if msg.reply_count > 0]

        if not thread_parents:
            return thread_messages

        logger.info(f"Loading thread replies for {len(thread_parents)} conversations")

        for parent_msg in thread_parents:
            thread_file = self.threads_dir / f"thread_{parent_msg.timestamp}.json"

            if not thread_file.exists():
                continue

            try:
                with open(thread_file, 'r', encoding='utf-8') as f:
                    thread_data = json.load(f)

                for reply_data in thread_data.get('replies', []):
                    slack_msg = self._create_slack_message(reply_data, filter_bots)
                    if slack_msg:
                        thread_messages.append(slack_msg)

                self.stats['threads_loaded'] += 1

            except Exception as e:
                logger.error(f"Failed to load thread {parent_msg.timestamp}: {e}")

        logger.info(f"Loaded {len(thread_messages)} thread replies")
        return thread_messages

    def _create_slack_message(self, msg_data: Dict[str, Any], filter_bots: bool) -> Optional[SlackMessage]:
        """Create SlackMessage object from staged message data."""
        # Filter bots if requested
        if filter_bots and self._is_bot_message(msg_data):
            return None

        # Get user information
        user_id = msg_data.get('user', '')
        user_info = self.user_cache.get(user_id, {})
        user_name = self._get_best_user_name(user_info, user_id)

        # Extract reactions
        reactions = []
        if 'reactions' in msg_data:
            reactions = [r['name'] for r in msg_data['reactions']]

        # Extract file information
        files = []
        if 'files' in msg_data:
            files = [{
                'id': f.get('id', ''),
                'name': f.get('name', ''),
                'mimetype': f.get('mimetype', ''),
                'size': f.get('size', 0),
                'url': f.get('url_private', '')
            } for f in msg_data['files']]

        return SlackMessage(
            text=msg_data.get('text', ''),
            user=user_id,
            timestamp=msg_data.get('ts', ''),
            thread_ts=msg_data.get('thread_ts'),
            channel=self.channel_id or "",
            user_name=user_name,
            reactions=reactions,
            reply_count=msg_data.get('reply_count', 0),
            files=files,
            edited=bool(msg_data.get('edited')),
            subtype=msg_data.get('subtype')
        )

    def _is_bot_message(self, msg_data: Dict[str, Any]) -> bool:
        """Check if message is from a bot."""
        # Check for bot subtype
        if msg_data.get('subtype') == 'bot_message':
            return True

        # Check if user is marked as bot
        user_id = msg_data.get('user', '')
        if user_id in self.user_cache:
            return self.user_cache[user_id].get('is_bot', False)

        # Check for bot_id field
        if msg_data.get('bot_id'):
            return True

        return False

    def _get_best_user_name(self, user_info: Dict[str, Any], user_id: str) -> str:
        """Get the best available user name."""
        if not user_info:
            return f"User_{user_id[:8]}" if user_id else "Unknown"

        # Priority: display_name > real_name > name > user_id
        return (user_info.get('display_name') or
                user_info.get('real_name') or
                user_info.get('name') or
                (f"User_{user_id[:8]}" if user_id else "Unknown"))

    def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """Get cached user information."""
        return self.user_cache.get(user_id, {
            'id': user_id,
            'best_name': f'User_{user_id[:8]}',
            'real_name': '',
            'display_name': '',
            'name': '',
            'title': '',
            'is_bot': False,
            'timezone': ''
        })

    def get_channel_info(self, channel_id: str) -> Dict[str, Any]:
        """Get cached channel information."""
        channel_info = self.metadata.get('channel', {})
        return {
            'id': channel_id,
            'name': channel_info.get('name', f'channel_{channel_id[:8]}'),
            'purpose': '',
            'topic': '',
            'is_private': channel_info.get('is_private', False),
            'member_count': 0
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get loading statistics."""
        return self.stats.copy()

    def clear_cache(self) -> None:
        """Clear any internal caches (no-op for staged loader)."""
        pass

    def fetch_channel_messages(self,
                             days_back: int = 30,
                             include_threads: bool = True,
                             filter_bots: bool = True,
                             date_filter: Optional[List[str]] = None) -> List[SlackMessage]:
        """
        Load messages from staged JSON files.

        Args:
            days_back: Number of days to look back (filters available dates)
            include_threads: Whether to include thread replies
            filter_bots: Whether to filter out bot messages
            date_filter: Specific dates to load (YYYY-MM-DD format)

        Returns:
            List of SlackMessage objects
        """
        start_time = datetime.now()
        logger.info(f"Loading staged messages for channel {self.channel_id}")

        messages = []

        # Determine which dates to load
        dates_to_load = self._determine_dates_to_load(days_back, date_filter)

        # Load messages from each date file
        for date_str in dates_to_load:
            date_messages = self._load_messages_for_date(date_str, filter_bots)
            messages.extend(date_messages)
            self.stats['files_processed'] += 1

        # Include thread replies if requested
        if include_threads:
            thread_messages = self._load_thread_messages(messages, filter_bots)
            messages.extend(thread_messages)

        # Sort messages by timestamp
        messages.sort(key=lambda x: float(x.timestamp))

        # Update statistics
        load_time = (datetime.now() - start_time).total_seconds()
        self.stats['messages_loaded'] = len(messages)
        self.stats['load_time_seconds'] = load_time

        logger.info(f"Loaded {len(messages)} messages in {load_time:.2f}s")
        return messages

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from local Slack JSON files using 500-token chunking strategy.

        Args:
            **kwargs: Additional arguments
                - channel_id: Optional channel ID to filter by
                - limit: Maximum number of documents to return (default: max_documents_per_channel)
                - days_back: Number of days to look back (default: 30)
                - filter_bots: Whether to filter out bot messages (default: True)
                - include_threads: Whether to include thread replies (default: True)
                - date_filter: Specific dates to load (YYYY-MM-DD format)
                - max_tokens: Maximum tokens per document (default: 500)
                - overlap_tokens: Tokens to overlap between documents (default: 50)

        Returns:
            List[Dict[str, Any]]: List of documents with 500-token chunks
        """
        # Extract parameters from kwargs
        channel_id = kwargs.get("channel_id", self.channel_id)
        limit = kwargs.get("limit", self.max_documents_per_channel)
        days_back = kwargs.get("days_back", self.custom_days)
        filter_bots = kwargs.get("filter_bots", True)
        include_threads = kwargs.get("include_threads", True)
        date_filter = kwargs.get("date_filter")
        max_tokens = kwargs.get("max_tokens", 500)
        overlap_tokens = kwargs.get("overlap_tokens", 50)

        # Load messages
        messages = self.fetch_channel_messages(
            days_back=days_back,
            include_threads=include_threads,
            filter_bots=filter_bots,
            date_filter=date_filter
        )

        if not messages:
            logger.warning(f"No messages found for channel {channel_id}")
            return []

        # Create 500-token documents with conversation awareness
        documents = self._create_token_based_documents(
            messages, max_tokens, overlap_tokens
        )

        # Apply limit
        if limit and len(documents) > limit:
            documents = documents[:limit]

        return documents

    def _create_token_based_documents(
        self,
        messages: List[SlackMessage],
        max_tokens: int = 500,
        overlap_tokens: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Create documents by merging messages up to 500 tokens with conversation awareness.

        This method implements the core 500-token chunking strategy with improved
        error handling, validation, and conversation context preservation.

        Args:
            messages: List of SlackMessage objects
            max_tokens: Maximum tokens per document
            overlap_tokens: Tokens to overlap between documents for context

        Returns:
            List of document dictionaries
        """
        if not messages:
            logger.warning("No messages provided for token-based document creation")
            return []

        # Validate parameters
        if max_tokens <= 0:
            logger.error(f"Invalid max_tokens: {max_tokens}. Using default 500.")
            max_tokens = 500

        if overlap_tokens < 0:
            logger.error(f"Invalid overlap_tokens: {overlap_tokens}. Using default 50.")
            overlap_tokens = 50

        if overlap_tokens >= max_tokens:
            logger.warning(f"Overlap tokens ({overlap_tokens}) >= max tokens ({max_tokens}). Reducing overlap.")
            overlap_tokens = max_tokens // 4  # Use 25% of max tokens for overlap

        # Sort messages by timestamp to ensure chronological order
        try:
            sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))
        except (ValueError, TypeError) as e:
            logger.error(f"Error sorting messages by timestamp: {e}")
            # Fallback: use messages as-is
            sorted_messages = messages

        # Group messages into conversation-aware chunks
        documents = []
        current_chunk = []
        current_tokens = 0
        overlap_content = ""

        for msg in sorted_messages:
            try:
                # Format message text with metadata
                msg_text = self._format_message_with_context(msg)
                msg_tokens = self._estimate_tokens(msg_text)

                # Skip empty messages
                if not msg_text.strip():
                    logger.debug(f"Skipping empty message: {msg.timestamp}")
                    continue

                # Check if adding this message would exceed token limit
                if current_tokens + msg_tokens > max_tokens and current_chunk:
                    # Create document from current chunk
                    try:
                        doc = self._create_document_from_message_chunk(
                            current_chunk, overlap_content
                        )
                        if doc:
                            documents.append(doc)
                        else:
                            logger.warning(f"Failed to create document from chunk of {len(current_chunk)} messages")
                    except Exception as e:
                        logger.error(f"Error creating document from message chunk: {e}")
                        continue

                    # Prepare overlap content from last few messages
                    try:
                        overlap_content = self._create_overlap_content(
                            current_chunk, overlap_tokens
                        )
                    except Exception as e:
                        logger.error(f"Error creating overlap content: {e}")
                        overlap_content = ""

                    # Start new chunk with current message
                    current_chunk = [msg]
                    # Account for overlap content in token count
                    overlap_token_count = self._estimate_tokens(overlap_content) if overlap_content else 0
                    current_tokens = overlap_token_count + msg_tokens
                else:
                    # Add message to current chunk
                    current_chunk.append(msg)
                    current_tokens += msg_tokens

            except Exception as e:
                logger.error(f"Error processing message {msg.timestamp}: {e}")
                continue

        # Handle remaining messages
        if current_chunk:
            try:
                doc = self._create_document_from_message_chunk(
                    current_chunk, overlap_content
                )
                if doc:
                    documents.append(doc)
                else:
                    logger.warning(f"Failed to create final document from chunk of {len(current_chunk)} messages")
            except Exception as e:
                logger.error(f"Error creating final document from message chunk: {e}")

        logger.info(f"Created {len(documents)} token-based documents from {len(messages)} messages")

        # Log statistics for debugging
        if documents:
            avg_tokens = sum(doc['metadata']['estimated_tokens'] for doc in documents) / len(documents)
            logger.debug(f"Average tokens per document: {avg_tokens:.1f}")
            logger.debug(f"Token range: {min(doc['metadata']['estimated_tokens'] for doc in documents)} - {max(doc['metadata']['estimated_tokens'] for doc in documents)}")

        return documents

    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text using improved heuristics.

        This method provides a more accurate token estimation than simple character counting
        by considering word boundaries, punctuation, and common patterns in Slack messages.

        Args:
            text: Text to estimate tokens for

        Returns:
            Estimated token count
        """
        if not text:
            return 0

        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())

        # Split by whitespace to get word-like tokens
        words = text.split()

        # Base token count from words
        token_count = len(words)

        # Add tokens for punctuation and special characters
        # Count punctuation that typically becomes separate tokens
        punctuation_tokens = len(re.findall(r'[.!?,:;()[\]{}"\'-]', text))

        # Add tokens for URLs, mentions, and special Slack formatting
        special_patterns = len(re.findall(r'<[@#!][^>]+>|https?://\S+|```[^`]*```|`[^`]+`', text))

        # Combine counts with weights
        total_tokens = token_count + (punctuation_tokens * 0.3) + (special_patterns * 2)

        return max(1, int(total_tokens))

    def _format_message_with_context(self, message: SlackMessage) -> str:
        """
        Format a message with conversation context and metadata.

        Args:
            message: SlackMessage to format

        Returns:
            Formatted message text
        """
        timestamp = datetime.fromtimestamp(float(message.timestamp)).strftime("%Y-%m-%d %H:%M:%S")

        # Base message format
        formatted = f"[{timestamp}] {message.user_name}: {message.text}"

        # Add thread context
        if message.thread_ts and message.thread_ts != message.timestamp:
            formatted += " [Reply]"
        elif message.reply_count > 0:
            formatted += f" [Thread: {message.reply_count} replies]"

        # Add reactions
        if message.reactions:
            reactions_str = ", ".join([f":{r}:" for r in message.reactions])
            formatted += f" [Reactions: {reactions_str}]"

        # Add files
        if message.files:
            files_str = ", ".join([f.get('name', 'file') for f in message.files])
            formatted += f" [Files: {files_str}]"

        return formatted

    def _create_overlap_content(self, messages: List[SlackMessage], overlap_tokens: int) -> str:
        """
        Create overlap content from the end of a message chunk with improved logic.

        This method ensures that overlap content maintains conversation coherence
        by including complete messages and respecting thread boundaries.

        Args:
            messages: List of messages in the chunk
            overlap_tokens: Target tokens for overlap

        Returns:
            Overlap content string
        """
        if not messages or overlap_tokens <= 0:
            return ""

        # Sort messages by timestamp to ensure proper order
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        # Start from the last messages and work backwards
        overlap_parts = []
        current_tokens = 0

        # Work backwards through messages
        for msg in reversed(sorted_messages):
            msg_text = self._format_message_with_context(msg)
            msg_tokens = self._estimate_tokens(msg_text)

            # Check if adding this message would exceed the overlap limit
            if current_tokens + msg_tokens <= overlap_tokens:
                overlap_parts.insert(0, msg_text)
                current_tokens += msg_tokens
            else:
                # If we can't fit the whole message, try to include at least one message
                # for context, even if it slightly exceeds the limit
                if not overlap_parts and msg_tokens <= overlap_tokens * 1.5:
                    overlap_parts.insert(0, msg_text)
                    current_tokens += msg_tokens
                break

        # Ensure we have some overlap content if possible
        if not overlap_parts and sorted_messages:
            # Include at least the last message for minimal context
            last_msg = sorted_messages[-1]
            last_msg_text = self._format_message_with_context(last_msg)
            overlap_parts.append(last_msg_text)

        return "\n\n".join(overlap_parts)

    def _create_document_from_message_chunk(
        self,
        messages: List[SlackMessage],
        overlap_content: str = ""
    ) -> Dict[str, Any]:
        """
        Create a document from a chunk of messages.

        Args:
            messages: List of messages in the chunk
            overlap_content: Overlap content from previous chunk

        Returns:
            Document dictionary
        """
        if not messages:
            return None

        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        # Format all messages
        message_texts = []
        if overlap_content:
            message_texts.append(f"[Previous context]\n{overlap_content}\n[/Previous context]")

        for msg in sorted_messages:
            message_texts.append(self._format_message_with_context(msg))

        content = "\n\n".join(message_texts)

        # Create document metadata
        start_time = datetime.fromtimestamp(float(sorted_messages[0].timestamp))
        end_time = datetime.fromtimestamp(float(sorted_messages[-1].timestamp))
        duration_minutes = (end_time - start_time).total_seconds() / 60

        # Extract participants and threads
        participants = list(set(msg.user_name for msg in sorted_messages))
        thread_count = len(set(msg.thread_ts for msg in sorted_messages if msg.thread_ts))

        # Calculate engagement metrics
        total_reactions = sum(len(msg.reactions) for msg in sorted_messages)
        total_files = sum(len(msg.files) for msg in sorted_messages)

        # Generate document ID
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        doc_id = f"slack_{self.channel_id}_{start_time.strftime('%Y%m%d_%H%M%S')}_{content_hash}"

        # Create title
        channel_info = self.get_channel_info(self.channel_id)
        channel_name = channel_info.get('name', f'channel_{self.channel_id}')

        if len(participants) == 1:
            title = f"{channel_name} - {participants[0]} ({len(sorted_messages)} messages)"
        else:
            title = f"{channel_name} - {len(participants)} participants ({len(sorted_messages)} messages)"

        # Create document
        document = {
            'id': doc_id,
            'title': title,
            'content': content,
            'metadata': {
                'channel_id': self.channel_id,
                'channel_name': channel_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_minutes': duration_minutes,
                'message_count': len(sorted_messages),
                'participants': participants,
                'participant_count': len(participants),
                'thread_count': thread_count,
                'total_reactions': total_reactions,
                'total_files': total_files,
                'has_overlap': bool(overlap_content),
                'estimated_tokens': self._estimate_tokens(content),
                'chunking_strategy': 'token_based_500',
                'max_tokens': 500,
                'has_threads': thread_count > 0,
                'has_files': total_files > 0,
                'engagement_score': min(1.0, (
                    len(participants) * 0.2 +
                    (total_reactions / len(sorted_messages)) * 0.3 +
                    (1.0 if thread_count > 0 else 0.0) * 0.2 +
                    min(len(sorted_messages) / 10.0, 1.0) * 0.3
                ))
            },
            'url': f"https://slack.com/archives/{self.channel_id}/p{sorted_messages[0].timestamp.replace('.', '')}",
            'content_type': 'text/slack'
        }

        return document

    def _create_conversation_aware_documents(
        self,
        messages: List[SlackMessage],
        conversation_gap_minutes: int,
        min_conversation_messages: int
    ) -> List[Dict[str, Any]]:
        """
        Create conversation-aware documents from messages.

        Args:
            messages: List of SlackMessage objects
            conversation_gap_minutes: Maximum time gap between messages in same conversation
            min_conversation_messages: Minimum messages required for a conversation

        Returns:
            List of document dictionaries
        """
        if not messages:
            return []

        # Group messages into conversations
        conversations = self._group_messages_into_conversations(
            messages, conversation_gap_minutes, min_conversation_messages
        )

        # Create documents from conversations
        documents = []
        for i, conversation in enumerate(conversations):
            if not conversation:
                continue

            # Create document ID
            start_time = datetime.fromtimestamp(float(conversation[0].timestamp))
            doc_id = f"slack_{self.channel_id}_conv_{start_time.strftime('%Y%m%d_%H%M%S')}_{i}"

            # Create document title
            channel_info = self.get_channel_info(self.channel_id)
            channel_name = channel_info.get('name', f'channel_{self.channel_id}')

            # Determine conversation type and create appropriate title
            conversation_type = self._determine_conversation_type(conversation)
            participants = list(set(msg.user_name for msg in conversation))

            if conversation_type == 'thread':
                title = f"{channel_name} - Thread Discussion ({len(participants)} participants)"
            elif conversation_type == 'q_and_a':
                title = f"{channel_name} - Q&A Session ({len(participants)} participants)"
            else:
                title = f"{channel_name} - Conversation ({len(participants)} participants)"

            # Create document content
            content = self._format_conversation_as_text(conversation)

            # Create enhanced metadata
            metadata = self._create_conversation_metadata(conversation, conversation_type)
            metadata.update({
                'channel_id': self.channel_id,
                'channel_name': channel_name,
                'chunking_strategy': 'conversation_aware',
                'conversation_index': i,
                'is_conversation_group': True
            })

            # Create document
            document = {
                'id': doc_id,
                'title': title,
                'content': content,
                'metadata': metadata,
                'url': f"https://slack.com/archives/{self.channel_id}/p{conversation[0].timestamp.replace('.', '')}",
                'content_type': 'text/slack'
            }

            documents.append(document)

        # Sort documents by start time (newest first)
        documents.sort(key=lambda x: x['metadata']['start_time'], reverse=True)

        logger.info(f"Created {len(documents)} conversation-aware documents from {len(conversations)} conversations")
        return documents

    def _group_messages_into_conversations(
        self,
        messages: List[SlackMessage],
        conversation_gap_minutes: int,
        min_conversation_messages: int
    ) -> List[List[SlackMessage]]:
        """Group messages into conversation clusters based on time gaps and thread relationships."""
        if not messages:
            return []

        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        # First, group by threads
        thread_groups = defaultdict(list)
        non_thread_messages = []

        for msg in sorted_messages:
            if msg.thread_ts:
                thread_groups[msg.thread_ts].append(msg)
            else:
                non_thread_messages.append(msg)

        conversations = []

        # Add thread groups as conversations (if they meet minimum size)
        for thread_ts, thread_messages in thread_groups.items():
            if len(thread_messages) >= min_conversation_messages:
                conversations.append(sorted(thread_messages, key=lambda x: float(x.timestamp)))

        # Group non-thread messages by time proximity
        if non_thread_messages:
            current_conversation = [non_thread_messages[0]]

            for i in range(1, len(non_thread_messages)):
                prev_msg = non_thread_messages[i-1]
                curr_msg = non_thread_messages[i]

                # Calculate time gap
                time_gap = float(curr_msg.timestamp) - float(prev_msg.timestamp)
                time_gap_minutes = time_gap / 60

                if time_gap_minutes <= conversation_gap_minutes:
                    current_conversation.append(curr_msg)
                else:
                    # Start new conversation if current one meets minimum size
                    if len(current_conversation) >= min_conversation_messages:
                        conversations.append(current_conversation)
                    current_conversation = [curr_msg]

            # Add the last conversation
            if len(current_conversation) >= min_conversation_messages:
                conversations.append(current_conversation)

        return conversations

    def _determine_conversation_type(self, conversation: List[SlackMessage]) -> str:
        """Determine the type of conversation based on message patterns."""
        if not conversation:
            return 'discussion'

        # Check if it's a thread
        thread_messages = [msg for msg in conversation if msg.thread_ts]
        if len(thread_messages) == len(conversation) and len(thread_messages) > 1:
            return 'thread'

        # Check for Q&A patterns
        question_count = sum(1 for msg in conversation if '?' in msg.text)
        if question_count > 0 and question_count / len(conversation) > 0.2:
            return 'q_and_a'

        return 'discussion'

    def _format_conversation_as_text(self, conversation: List[SlackMessage]) -> str:
        """Format conversation messages as text with enhanced context."""
        if not conversation:
            return ""

        # Sort messages by timestamp
        sorted_messages = sorted(conversation, key=lambda x: float(x.timestamp))

        # Format messages with conversation context
        formatted_messages = []
        for i, msg in enumerate(sorted_messages):
            # Format timestamp
            timestamp = datetime.fromtimestamp(float(msg.timestamp)).strftime("%Y-%m-%d %H:%M:%S")

            # Format message with context indicators
            formatted_msg = f"[{timestamp}] {msg.user_name}: {msg.text}"

            # Add thread indicator
            if msg.thread_ts and msg.thread_ts != msg.timestamp:
                formatted_msg += " [Thread Reply]"
            elif msg.reply_count > 0:
                formatted_msg += f" [Thread Parent - {msg.reply_count} replies]"

            # Add reactions if any
            if msg.reactions:
                reactions_str = ", ".join([f":{r}:" for r in msg.reactions])
                formatted_msg += f" [{reactions_str}]"

            # Add files if any
            if msg.files:
                files_str = ", ".join([f.get('name', 'file') for f in msg.files])
                formatted_msg += f" [Files: {files_str}]"

            formatted_messages.append(formatted_msg)

        return "\n\n".join(formatted_messages)

    def _create_conversation_metadata(self, conversation: List[SlackMessage], conversation_type: str) -> Dict[str, Any]:
        """Create enhanced metadata for conversation documents."""
        if not conversation:
            return {}

        # Sort messages by timestamp
        sorted_messages = sorted(conversation, key=lambda x: float(x.timestamp))

        # Extract time information
        start_time = datetime.fromtimestamp(float(sorted_messages[0].timestamp))
        end_time = datetime.fromtimestamp(float(sorted_messages[-1].timestamp))
        duration_minutes = (end_time - start_time).total_seconds() / 60

        # Extract participants
        participants = list(set(msg.user_name for msg in conversation))

        # Extract engagement metrics
        total_reactions = sum(len(msg.reactions) for msg in conversation)
        total_files = sum(len(msg.files) for msg in conversation)
        thread_count = len(set(msg.thread_ts for msg in conversation if msg.thread_ts))

        # Calculate engagement score
        engagement_score = min(1.0, (
            len(participants) * 0.2 +
            (total_reactions / len(conversation)) * 0.3 +
            (1.0 if thread_count > 0 else 0.0) * 0.2 +
            min(len(conversation) / 10.0, 1.0) * 0.3
        ))

        return {
            'conversation_type': conversation_type,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_minutes': duration_minutes,
            'message_count': len(conversation),
            'participants': participants,
            'participant_count': len(participants),
            'thread_count': thread_count,
            'total_reactions': total_reactions,
            'total_files': total_files,
            'engagement_score': engagement_score,
            'has_questions': any('?' in msg.text for msg in conversation),
            'has_threads': thread_count > 0,
            'has_files': total_files > 0
        }

    def _create_time_based_documents(self, messages: List[SlackMessage], time_period: str, custom_days: int) -> List[Dict[str, Any]]:
        """
        Create time-based documents from messages.

        Args:
            messages: List of SlackMessage objects
            time_period: Time period for grouping (monthly, weekly, daily, custom)
            custom_days: Number of days for custom time period

        Returns:
            List of document dictionaries
        """
        if not messages:
            return []

        # Group messages by time period
        grouped_messages = self._group_messages_by_time_period(messages, time_period, custom_days)

        # Create documents from grouped messages
        documents = []
        for period, period_messages in grouped_messages.items():
            if not period_messages:
                continue

            # Create document ID
            doc_id = f"slack_{self.channel_id}_{period}"

            # Create document title
            channel_info = self.get_channel_info(self.channel_id)
            channel_name = channel_info.get('name', f'channel_{self.channel_id}')

            if time_period == "monthly":
                title = f"{channel_name} - {period[:7]}"  # YYYY-MM
            elif time_period == "weekly":
                # Extract week start date
                week_start = period.split('_')[0]
                title = f"{channel_name} - Week of {week_start}"
            elif time_period == "daily":
                title = f"{channel_name} - {period}"  # YYYY-MM-DD
            else:  # custom
                # Extract date range
                date_range = period.replace('_', ' to ')
                title = f"{channel_name} - {date_range}"

            # Create document content
            content = self._format_messages_as_text(period_messages)

            # Create document metadata
            metadata = {
                'channel_id': self.channel_id,
                'channel_name': channel_name,
                'time_period': time_period,
                'period': period,
                'message_count': len(period_messages),
                'users': self._extract_unique_users(period_messages),
                'reactions': self._extract_reactions(period_messages),
                'has_threads': any(msg.is_thread_parent for msg in period_messages),
                'thread_count': sum(1 for msg in period_messages if msg.is_thread_parent),
                'chunking_strategy': time_period,
                'is_time_group': True
            }

            # Create document
            document = {
                'id': doc_id,
                'title': title,
                'content': content,
                'metadata': metadata,
                'url': f"https://slack.com/archives/{self.channel_id}/p{period_messages[0].timestamp.replace('.', '')}",
                'content_type': 'text/slack'
            }

            documents.append(document)

        # Sort documents by period (newest first)
        documents.sort(key=lambda x: x['metadata']['period'], reverse=True)

        return documents

    def _group_messages_by_time_period(self, messages: List[SlackMessage], time_period: str, custom_days: int) -> Dict[str, List[SlackMessage]]:
        """
        Group messages by time period.

        Args:
            messages: List of SlackMessage objects
            time_period: Time period for grouping (monthly, weekly, daily, custom)
            custom_days: Number of days for custom time period

        Returns:
            Dictionary mapping time periods to lists of messages
        """
        if not messages:
            return {}

        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        grouped_messages = {}

        if time_period == "monthly":
            # Group by month (YYYY-MM)
            for msg in sorted_messages:
                dt = datetime.fromtimestamp(float(msg.timestamp))
                period = dt.strftime("%Y-%m")
                if period not in grouped_messages:
                    grouped_messages[period] = []
                grouped_messages[period].append(msg)

        elif time_period == "weekly":
            # Group by week (YYYY-MM-DD_YYYY-MM-DD)
            for msg in sorted_messages:
                dt = datetime.fromtimestamp(float(msg.timestamp))
                # Get the start of the week (Monday)
                start_of_week = dt - timedelta(days=dt.weekday())
                end_of_week = start_of_week + timedelta(days=6)
                period = f"{start_of_week.strftime('%Y-%m-%d')}_{end_of_week.strftime('%Y-%m-%d')}"
                if period not in grouped_messages:
                    grouped_messages[period] = []
                grouped_messages[period].append(msg)

        elif time_period == "daily":
            # Group by day (YYYY-MM-DD)
            for msg in sorted_messages:
                dt = datetime.fromtimestamp(float(msg.timestamp))
                period = dt.strftime("%Y-%m-%d")
                if period not in grouped_messages:
                    grouped_messages[period] = []
                grouped_messages[period].append(msg)

        else:  # custom
            # Group by custom time period
            if not sorted_messages:
                return {}

            # Get the earliest and latest timestamps
            earliest_dt = datetime.fromtimestamp(float(sorted_messages[0].timestamp))
            latest_dt = datetime.fromtimestamp(float(sorted_messages[-1].timestamp))

            # Create time periods
            current_start = earliest_dt
            while current_start <= latest_dt:
                current_end = current_start + timedelta(days=custom_days - 1)
                period = f"{current_start.strftime('%Y-%m-%d')}_{current_end.strftime('%Y-%m-%d')}"
                grouped_messages[period] = []
                current_start = current_end + timedelta(days=1)

            # Assign messages to periods
            for msg in sorted_messages:
                dt = datetime.fromtimestamp(float(msg.timestamp))
                for period in grouped_messages:
                    start_str, end_str = period.split('_')
                    start_dt = datetime.strptime(start_str, "%Y-%m-%d")
                    end_dt = datetime.strptime(end_str, "%Y-%m-%d")
                    if start_dt <= dt <= end_dt:
                        grouped_messages[period].append(msg)
                        break

        return grouped_messages

    def _format_messages_as_text(self, messages: List[SlackMessage]) -> str:
        """
        Format messages as text for document content.

        Args:
            messages: List of SlackMessage objects

        Returns:
            Formatted text
        """
        if not messages:
            return ""

        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        # Format messages
        formatted_messages = []
        for msg in sorted_messages:
            # Format timestamp
            timestamp = datetime.fromtimestamp(float(msg.timestamp)).strftime("%Y-%m-%d %H:%M:%S")

            # Format message
            formatted_msg = f"[{timestamp}] {msg.user_name}: {msg.text}"

            # Add reactions if any
            if msg.reactions:
                reactions_str = ", ".join([f":{r}:" for r in msg.reactions])
                formatted_msg += f" [{reactions_str}]"

            # Add files if any
            if msg.files:
                files_str = ", ".join([f.get('name', 'file') for f in msg.files])
                formatted_msg += f" [Files: {files_str}]"

            formatted_messages.append(formatted_msg)

        return "\n\n".join(formatted_messages)

    def _extract_unique_users(self, messages: List[SlackMessage]) -> Dict[str, str]:
        """
        Extract unique users from messages.

        Args:
            messages: List of SlackMessage objects

        Returns:
            Dictionary mapping user IDs to user names
        """
        users = {}
        for msg in messages:
            if msg.user and msg.user not in users:
                users[msg.user] = msg.user_name
        return users

    def _extract_reactions(self, messages: List[SlackMessage]) -> Dict[str, int]:
        """
        Extract reactions from messages.

        Args:
            messages: List of SlackMessage objects

        Returns:
            Dictionary mapping reaction names to counts
        """
        reaction_counts = defaultdict(int)
        for msg in messages:
            for reaction in msg.reactions:
                reaction_counts[reaction] += 1
        return dict(reaction_counts)

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document by ID.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments for getting the document

        Returns:
            Dict[str, Any]: Document
        """
        # Extract channel ID and period from document ID
        # Format: slack_CHANNEL_ID_PERIOD
        parts = document_id.split('_', 2)
        if len(parts) != 3 or parts[0] != 'slack':
            raise ValueError(f"Invalid document ID format: {document_id}")

        channel_id = parts[1]
        period = parts[2]

        # Load messages for the period
        if '-' in period and '_' not in period:
            # Daily or monthly period
            if len(period) == 7:  # YYYY-MM
                time_period = "monthly"
                date_filter = [f"{period}-01"]  # First day of the month
            else:  # YYYY-MM-DD
                time_period = "daily"
                date_filter = [period]
        elif '_' in period:
            # Weekly or custom period
            start_date, end_date = period.split('_')
            if (datetime.strptime(end_date, "%Y-%m-%d") - datetime.strptime(start_date, "%Y-%m-%d")).days == 6:
                time_period = "weekly"
            else:
                time_period = "custom"
            date_filter = [start_date]
        else:
            raise ValueError(f"Invalid period format: {period}")

        # Load messages
        messages = self.fetch_channel_messages(
            days_back=730,  # Look back a year to ensure we find the period
            include_threads=True,
            filter_bots=False,  # Include all messages
            date_filter=date_filter
        )

        if not messages:
            raise ValueError(f"No messages found for document ID: {document_id}")

        # Group messages by the specified time period
        grouped_messages = self._group_messages_by_time_period(
            messages,
            time_period,
            custom_days=30  # Default custom days
        )

        # Find the period in grouped messages
        if period not in grouped_messages:
            raise ValueError(f"Period not found: {period}")

        period_messages = grouped_messages[period]

        # Create document
        channel_info = self.get_channel_info(channel_id)
        channel_name = channel_info.get('name', f'channel_{channel_id}')

        if time_period == "monthly":
            title = f"{channel_name} - {period[:7]}"  # YYYY-MM
        elif time_period == "weekly":
            # Extract week start date
            week_start = period.split('_')[0]
            title = f"{channel_name} - Week of {week_start}"
        elif time_period == "daily":
            title = f"{channel_name} - {period}"  # YYYY-MM-DD
        else:  # custom
            # Extract date range
            date_range = period.replace('_', ' to ')
            title = f"{channel_name} - {date_range}"

        # Create document content
        content = self._format_messages_as_text(period_messages)

        # Create document metadata
        metadata = {
            'channel_id': channel_id,
            'channel_name': channel_name,
            'time_period': time_period,
            'period': period,
            'message_count': len(period_messages),
            'users': self._extract_unique_users(period_messages),
            'reactions': self._extract_reactions(period_messages),
            'has_threads': any(msg.is_thread_parent for msg in period_messages),
            'thread_count': sum(1 for msg in period_messages if msg.is_thread_parent),
            'chunking_strategy': time_period,
            'is_time_group': True
        }

        # Create document
        document = {
            'id': document_id,
            'title': title,
            'content': content,
            'metadata': metadata,
            'url': f"https://slack.com/archives/{channel_id}/p{period_messages[0].timestamp.replace('.', '')}",
            'content_type': 'text/slack'
        }

        return document

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in the source.

        Args:
            query: Query to search for
            **kwargs: Additional arguments for searching documents
                - channel_id: Specific channel ID to search
                - limit: Maximum number of documents to return
                - days_back: Number of days to look back

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        # Extract parameters from kwargs
        channel_id = kwargs.get("channel_id", self.channel_id)
        limit = kwargs.get("limit", 10)
        days_back = kwargs.get("days_back", self.custom_days)

        # Load all messages
        messages = self.fetch_channel_messages(
            days_back=days_back,
            include_threads=True,
            filter_bots=False,  # Include all messages for search
            date_filter=None
        )

        if not messages:
            logger.warning(f"No messages found for channel {channel_id}")
            return []

        # Simple keyword search in message text
        query_terms = query.lower().split()
        matching_messages = []

        for msg in messages:
            text = msg.text.lower()
            # Check if all query terms are in the message text
            if all(term in text for term in query_terms):
                matching_messages.append(msg)

        if not matching_messages:
            logger.info(f"No messages matching query: {query}")
            return []

        # Group matching messages by time period
        time_period = kwargs.get("time_period", self.time_period)
        custom_days = kwargs.get("custom_days", self.custom_days)

        # Create documents from matching messages
        grouped_messages = self._group_messages_by_time_period(matching_messages, time_period, custom_days)

        # Create documents
        documents = []
        for period, period_messages in grouped_messages.items():
            if not period_messages:
                continue

            # Create document ID
            doc_id = f"slack_{channel_id}_{period}"

            # Create document title
            channel_info = self.get_channel_info(channel_id)
            channel_name = channel_info.get('name', f'channel_{channel_id}')

            if time_period == "monthly":
                title = f"{channel_name} - {period[:7]}"  # YYYY-MM
            elif time_period == "weekly":
                # Extract week start date
                week_start = period.split('_')[0]
                title = f"{channel_name} - Week of {week_start}"
            elif time_period == "daily":
                title = f"{channel_name} - {period}"  # YYYY-MM-DD
            else:  # custom
                # Extract date range
                date_range = period.replace('_', ' to ')
                title = f"{channel_name} - {date_range}"

            # Create document content
            content = self._format_messages_as_text(period_messages)

            # Create document metadata
            metadata = {
                'channel_id': channel_id,
                'channel_name': channel_name,
                'time_period': time_period,
                'period': period,
                'message_count': len(period_messages),
                'users': self._extract_unique_users(period_messages),
                'reactions': self._extract_reactions(period_messages),
                'has_threads': any(msg.is_thread_parent for msg in period_messages),
                'thread_count': sum(1 for msg in period_messages if msg.is_thread_parent),
                'chunking_strategy': time_period,
                'is_time_group': True,
                'search_query': query,
                'match_count': len(period_messages)
            }

            # Create document
            document = {
                'id': doc_id,
                'title': title,
                'content': content,
                'metadata': metadata,
                'url': f"https://slack.com/archives/{channel_id}/p{period_messages[0].timestamp.replace('.', '')}",
                'content_type': 'text/slack'
            }

            documents.append(document)

        # Sort documents by match count (most matches first)
        documents.sort(key=lambda x: x['metadata']['match_count'], reverse=True)

        # Apply limit
        if limit and len(documents) > limit:
            documents = documents[:limit]

        return documents

    def get_pagination_cursor(self) -> Optional[str]:
        """
        Get the current pagination cursor.

        Returns:
            Optional[str]: Current pagination cursor or None if not available
        """
        return self.pagination_cursor

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "local_slack"

    def validate_config(self) -> bool:
        """
        Validate the configuration with comprehensive error checking.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        # Check if data directory exists
        if not os.path.exists(self.data_dir):
            logger.error(f"Data directory {self.data_dir} does not exist")
            return False

        # Check if channel directory exists
        if not self.channel_dir:
            logger.error(f"No channel directory found in {self.data_dir}")
            logger.info(f"Expected directory format: channel_{{CHANNEL_ID}} in {self.data_dir}")
            return False

        # Check if messages directory exists
        if not hasattr(self, 'messages_dir') or not self.messages_dir or not self.messages_dir.exists():
            logger.error(f"Messages directory not found in {self.channel_dir}")
            logger.info(f"Expected: {self.channel_dir}/messages/")
            return False

        # Check if there are any message files
        try:
            message_files = list(self.messages_dir.glob("messages_*.json"))
            if not message_files:
                logger.error(f"No message files found in {self.messages_dir}")
                logger.info(f"Expected files: messages_YYYY-MM-DD.json")
                return False
            else:
                logger.info(f"Found {len(message_files)} message files")
        except Exception as e:
            logger.error(f"Error checking message files: {e}")
            return False

        # Check for users.json (warning only)
        if not hasattr(self, 'users_dir') or not self.users_dir or not (self.users_dir / "users.json").exists():
            logger.warning(f"users.json not found. User resolution will be limited.")
        else:
            logger.info(f"Found user cache with {len(self.user_cache)} users")

        # Check for threads directory (optional)
        if hasattr(self, 'threads_dir') and self.threads_dir and self.threads_dir.exists():
            thread_files = list(self.threads_dir.glob("thread_*.json"))
            logger.info(f"Found {len(thread_files)} thread files")

        # Validate channel ID format
        if self.channel_id and not re.match(r'^C[A-Z0-9]+$', self.channel_id):
            logger.warning(f"Channel ID {self.channel_id} doesn't match expected Slack format")

        logger.info(f"Configuration validation passed for channel {self.channel_id}")
        return True



    def _process_channel_messages(self, messages: List[Dict[str, Any]], channel_name: str) -> List[Dict[str, Any]]:
        """
        Process channel messages using a time-based aggregation strategy.

        This implementation creates a single document per time period (month, week, etc.) for each channel,
        which is more efficient since the ingestion service already handles further chunking.

        Args:
            messages: List of messages to process
            channel_name: Name of the channel

        Returns:
            List[Dict[str, Any]]: List of processed documents
        """
        # Flatten and normalize messages
        all_msgs = self._normalize_messages(messages, channel_name)

        if not all_msgs:
            return []

        # Group messages by time period
        time_period_messages = defaultdict(list)

        for msg in all_msgs:
            # Convert timestamp to datetime
            msg_time = self._to_datetime(msg["ts"])

            # Create a time period key based on configuration
            if self.time_period == "monthly":
                # Monthly: YYYY-MM
                period_key = f"{msg_time.year}-{msg_time.month:02d}"
            elif self.time_period == "weekly":
                # Weekly: YYYY-WW (week number)
                week_num = msg_time.isocalendar()[1]
                period_key = f"{msg_time.year}-W{week_num:02d}"
            elif self.time_period == "daily":
                # Daily: YYYY-MM-DD
                period_key = msg_time.strftime("%Y-%m-%d")
            elif self.time_period == "custom":
                # Custom period: Group by N days from the start of data
                # Find the earliest message time
                if not hasattr(self, '_earliest_msg_time'):
                    # Find earliest message time across all messages
                    earliest_ts = min(all_msgs, key=lambda m: float(m["ts"]))["ts"]
                    self._earliest_msg_time = self._to_datetime(earliest_ts)

                # Calculate days since earliest message
                days_since_start = (msg_time - self._earliest_msg_time).days
                period_num = days_since_start // self.custom_days
                period_key = f"period-{period_num}"
            else:
                # Default to monthly if invalid time period
                period_key = f"{msg_time.year}-{msg_time.month:02d}"

            time_period_messages[period_key].append(msg)

        # Create one document per time period
        documents = []

        for period_key, period_msgs in time_period_messages.items():
            # Sort messages by timestamp
            period_msgs.sort(key=lambda x: x["ts"])

            # Create a document for this time period
            doc = self._create_time_period_document(period_msgs, channel_name, period_key)
            if doc:
                documents.append(doc)

        logger.info(f"Created {len(documents)} {self.time_period} documents for channel {channel_name}")
        return documents

    def _create_time_period_document(self, messages: List[Dict[str, Any]], channel_name: str, period_key: str) -> Dict[str, Any]:
        """
        Create a document containing all messages for a time period.

        Args:
            messages: List of messages for the time period
            channel_name: Name of the channel
            period_key: Time period identifier (e.g., YYYY-MM for monthly)

        Returns:
            Dict[str, Any]: Document or None if invalid
        """
        if not messages:
            return None

        # Sort messages by timestamp
        messages.sort(key=lambda x: x["ts"])

        # Create content with clear message separation and threading information
        content_parts = []

        # Track threads to maintain conversation context
        threads = defaultdict(list)
        for msg in messages:
            threads[msg["thread_ts"]].append(msg)

        # Process each thread
        for thread_ts, thread_msgs in threads.items():
            # Sort thread messages
            thread_msgs.sort(key=lambda x: x["ts"])

            # Get the parent message (first message in thread)
            parent_msg = thread_msgs[0]

            # Format the parent message with date and time
            parent_time = self._to_datetime(parent_msg["ts"])
            parent_content = f"[{parent_time.strftime('%Y-%m-%d %H:%M:%S')}] {parent_msg['user']}: {parent_msg['text']}"

            # Add code blocks if present
            if parent_msg.get("code_blocks"):
                for code_block in parent_msg["code_blocks"]:
                    parent_content += f"\n```{code_block['language']}\n{code_block['content']}\n```"

            thread_content = [parent_content]

            # Add replies if this is a thread with multiple messages
            if len(thread_msgs) > 1:
                thread_content.append("\nReplies:")
                for reply in thread_msgs[1:]:
                    reply_time = self._to_datetime(reply["ts"])
                    reply_content = f"  [{reply_time.strftime('%Y-%m-%d %H:%M:%S')}] {reply['user']}: {reply['text']}"

                    # Add code blocks if present
                    if reply.get("code_blocks"):
                        for code_block in reply["code_blocks"]:
                            reply_content += f"\n  ```{code_block['language']}\n  {code_block['content']}\n  ```"

                    thread_content.append(reply_content)

            # Add thread to content with clear separation and thread ID for reference
            thread_header = f"Thread ID: {thread_ts}"
            thread_content.insert(0, thread_header)
            content_parts.append("\n".join(thread_content))

        # Join all threads with clear separation
        content = "\n\n---\n\n".join(content_parts)

        # Skip empty content
        if not content.strip():
            return None

        # Get time period type and format a human-readable period name
        if self.time_period == "monthly":
            period_type = "monthly"
            period_name = period_key  # Already in YYYY-MM format
        elif self.time_period == "weekly":
            period_type = "weekly"
            # Extract year and week number from period_key (format: YYYY-WXX)
            period_name = period_key
        elif self.time_period == "daily":
            period_type = "daily"
            period_name = period_key  # Already in YYYY-MM-DD format
        elif self.time_period == "custom":
            period_type = f"custom-{self.custom_days}day"
            period_name = period_key
        else:
            period_type = "monthly"
            period_name = period_key

        # Create document ID
        doc_id = f"{channel_name}-{period_type}-{period_key}-{self._hash_content(content)}"

        # Generate title
        title = f"Channel: {channel_name} - {period_name}"

        # Calculate quality score
        quality_score = self._assess_quality(messages, content)

        # Extract technical terms
        technical_terms = self._extract_technical_terms(content)

        # Create document
        doc = {
            "id": doc_id,
            "title": title,
            "content": content,
            "content_type": "text/slack",
            "metadata": {
                "chunking_strategy": period_type,
                "channel": channel_name,
                "period_key": period_key,
                "period_type": period_type,
                "participants": list({m["user"] for m in messages}),
                "start_time": self._to_datetime(messages[0]["ts"]).isoformat(),
                "end_time": self._to_datetime(messages[-1]["ts"]).isoformat(),
                "message_count": len(messages),
                "thread_count": len(threads),
                "reactions": self._extract_reactions(messages),
                "has_code": any(m.get("code_blocks") for m in messages),
                "quality_score": quality_score,
                "technical_terms": technical_terms
            }
        }

        return doc

    def _normalize_messages(self, messages: List[Dict[str, Any]], channel_name: str) -> List[Dict[str, Any]]:
        """
        Normalize and flatten messages from different formats.

        Args:
            messages: List of messages to normalize
            channel_name: Name of the channel

        Returns:
            List[Dict[str, Any]]: Normalized messages
        """
        all_msgs = []

        for msg in messages:
            # Skip non-message types
            if msg.get("type") and msg.get("type") != "message":
                continue

            # Basic message normalization
            base = {
                "ts": msg["ts"],
                "user": msg.get("user", "unknown"),
                "text": msg.get("text", ""),
                "thread_ts": msg.get("thread_ts", msg["ts"]),
                "channel": channel_name,
                "reactions": msg.get("reactions", []),
                "reply_count": msg.get("reply_count", 0),
                "has_attachments": bool(msg.get("attachments")),
                "has_files": bool(msg.get("files")),
                "is_bot": bool(msg.get("bot_id")),
            }

            # Extract code blocks
            base["code_blocks"] = self._extract_code_blocks(base["text"])

            all_msgs.append(base)

            # Process replies if available
            for reply in msg.get("replies", []):
                reply_msg = {
                    "ts": reply["ts"],
                    "user": reply.get("user", "unknown"),
                    "text": reply.get("text", ""),
                    "thread_ts": msg["ts"],
                    "channel": base["channel"],
                    "reactions": reply.get("reactions", []),
                    "reply_count": 0,
                    "has_attachments": False,
                    "has_files": False,
                    "is_bot": False,
                }

                # Extract code blocks
                reply_msg["code_blocks"] = self._extract_code_blocks(reply_msg["text"])

                all_msgs.append(reply_msg)

        return all_msgs

    def _create_document_from_messages(self, messages: List[Dict[str, Any]],
                                      chunking_strategy: str,
                                      topic_id: int = None) -> Dict[str, Any]:
        """
        Create a document from a list of messages.

        Args:
            messages: List of messages to create a document from
            chunking_strategy: Chunking strategy used
            topic_id: Optional topic ID for topic-based chunking

        Returns:
            Dict[str, Any]: Document or None if invalid
        """
        if not messages:
            return None

        # Sort messages by timestamp
        messages.sort(key=lambda x: x["ts"])

        # Create content
        content = "\n".join([f"{m['user']}: {m['text']}" for m in messages])

        # Skip empty content
        if not content.strip():
            return None

        # Create document ID
        if chunking_strategy == "topic" and topic_id is not None:
            # For topic-based chunking, include topic ID in the hash
            doc_id = f"{messages[0]['channel']}-topic-{topic_id}-{self._hash_content(content)}"
        else:
            # For other strategies, use channel, strategy, and thread
            thread_ts = messages[0]["thread_ts"]
            doc_id = f"{messages[0]['channel']}-{chunking_strategy}-{thread_ts}-{self._hash_content(content)}"

        # Generate title
        title = self._generate_title(messages, chunking_strategy)

        # Calculate quality score
        quality_score = self._assess_quality(messages, content)

        # Create summary if enabled
        summary = None
        if self.enable_summary and self.summarizer:
            try:
                summary = self.summarizer(content)
            except Exception as e:
                logger.error(f"Error generating summary: {str(e)}")

        # Extract technical terms
        technical_terms = self._extract_technical_terms(content)

        # Create document
        doc = {
            "id": doc_id,
            "title": title,
            "content": content,
            "content_type": "text/slack",
            "metadata": {
                "chunking_strategy": chunking_strategy,
                "channel": messages[0]["channel"],
                "thread_ts": messages[0]["thread_ts"],
                "participants": list({m["user"] for m in messages}),
                "start_time": self._to_datetime(messages[0]["ts"]).isoformat(),
                "end_time": self._to_datetime(messages[-1]["ts"]).isoformat(),
                "message_count": len(messages),
                "reactions": self._extract_reactions(messages),
                "reply_count": sum(m.get("reply_count", 0) for m in messages),
                "has_code": any(m.get("code_blocks") for m in messages),
                "quality_score": quality_score,
                "technical_terms": technical_terms,
                "summary": summary
            }
        }

        # Add topic ID for topic-based chunking
        if chunking_strategy == "topic" and topic_id is not None:
            doc["metadata"]["topic_id"] = topic_id

        return doc

    def _generate_title(self, messages: List[Dict[str, Any]], chunking_strategy: str) -> str:
        """
        Generate a title for a document based on its messages.

        Args:
            messages: List of messages
            chunking_strategy: Chunking strategy used

        Returns:
            str: Generated title
        """
        if not messages:
            return "Empty Document"

        # Get first message text
        first_msg = messages[0]["text"]

        # Clean and truncate
        title = first_msg.split("\n")[0].strip()
        if len(title) > 50:
            title = title[:47] + "..."

        # Add prefix based on chunking strategy
        if chunking_strategy == "individual":
            return f"Message: {title}"
        elif chunking_strategy == "thread":
            return f"Thread: {title}"
        elif chunking_strategy == "window":
            return f"Conversation: {title}"
        elif chunking_strategy == "topic":
            return f"Topic: {title}"

        return title

    def _assess_quality(self, messages: List[Dict[str, Any]], content: str) -> float:
        """
        Assess the quality of a document.

        Args:
            messages: List of messages
            content: Document content

        Returns:
            float: Quality score between 0 and 1
        """
        # Base score
        score = 0.5

        # Adjust based on content length
        content_length = len(content)
        if content_length > 1000:
            score += 0.15
        elif content_length > 500:
            score += 0.1
        elif content_length < 100:
            score -= 0.1

        # Adjust based on message count
        msg_count = len(messages)
        if msg_count > 5:
            score += 0.1
        elif msg_count > 2:
            score += 0.05

        # Adjust based on code blocks
        code_block_count = sum(len(m.get("code_blocks", [])) for m in messages)
        if code_block_count > 0:
            score += min(0.2, code_block_count * 0.05)

        # Adjust based on reactions
        reaction_count = sum(count for reaction, count in self._extract_reactions(messages).items())
        if reaction_count > 0:
            score += min(0.1, reaction_count * 0.02)

        # Adjust based on technical terms
        technical_term_count = len(self._extract_technical_terms(content))
        if technical_term_count > 0:
            score += min(0.1, technical_term_count * 0.02)

        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, score))

    def _extract_code_blocks(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract code blocks from text.

        Args:
            text: Text to extract code blocks from

        Returns:
            List[Dict[str, Any]]: List of code blocks with language and content
        """
        if not text:
            return []

        # Match code blocks with language specification
        # ```python
        # code
        # ```
        code_blocks = []
        pattern = r"```([\w-]*)?\n(.*?)```"
        matches = re.findall(pattern, text, re.DOTALL)

        for lang, code in matches:
            code_blocks.append({
                "language": lang.strip() if lang else "unknown",
                "content": code.strip()
            })

        return code_blocks

    def _extract_technical_terms(self, text: str) -> List[str]:
        """
        Extract technical terms from text.

        Args:
            text: Text to extract technical terms from

        Returns:
            List[str]: List of technical terms
        """
        # This is a simplified implementation
        # In a real implementation, use NLP or a technical term dictionary

        # Common technical terms in software development
        tech_terms = [
            "api", "function", "class", "method", "variable", "database", "query",
            "server", "client", "endpoint", "request", "response", "json", "http",
            "rest", "graphql", "websocket", "docker", "kubernetes", "container",
            "microservice", "architecture", "framework", "library", "package",
            "module", "component", "interface", "implementation", "algorithm",
            "data structure", "cache", "index", "query", "transaction", "schema",
            "model", "view", "controller", "mvc", "orm", "sql", "nosql", "redis",
            "mongodb", "postgresql", "mysql", "elasticsearch", "vector", "embedding",
            "neural network", "machine learning", "ai", "nlp", "transformer", "bert",
            "gpt", "llm", "rag", "retrieval", "augmented", "generation"
        ]

        # Find matches (case insensitive)
        found_terms = set()
        text_lower = text.lower()

        for term in tech_terms:
            if term in text_lower:
                found_terms.add(term)

        return list(found_terms)

    def _chunk_by_time(self, messages: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """
        Chunk messages by time windows.

        Args:
            messages: List of messages to chunk

        Returns:
            List[List[Dict[str, Any]]]: List of message chunks
        """
        if not messages:
            return []

        messages.sort(key=lambda x: x["ts"])
        chunks, current = [], [messages[0]]

        for i in range(1, len(messages)):
            prev, curr = self._to_datetime(messages[i-1]["ts"]), self._to_datetime(messages[i]["ts"])
            if (curr - prev).total_seconds() / 60 <= self.max_gap_minutes:
                current.append(messages[i])
            else:
                chunks.append(current)
                current = [messages[i]]

        if current:
            chunks.append(current)

        return chunks

    def _to_datetime(self, ts: str) -> datetime:
        """Convert Slack timestamp to datetime."""
        try:
            return datetime.fromtimestamp(float(ts.split('.')[0]))
        except (ValueError, TypeError):
            # Fallback for invalid timestamps
            return datetime.now()

    def _hash_content(self, text: str) -> str:
        """Generate a hash for the content."""
        return hashlib.md5(text.encode()).hexdigest()

    def _extract_reactions(self, messages: List[SlackMessage]) -> Dict[str, int]:
        """Extract reaction counts from messages."""
        reaction_counts = defaultdict(int)
        for msg in messages:
            # Handle SlackMessage objects - access reactions attribute directly
            for reaction in msg.reactions:
                # Reactions in SlackMessage are stored as strings, not dicts
                # For now, just count unique reactions
                if reaction:
                    reaction_counts[reaction] += 1
        return dict(reaction_counts)

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document by ID.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments
                - time_period: Override the default time period (monthly, weekly, daily, custom)
                - custom_days: Number of days for custom time period

        Returns:
            Dict[str, Any]: Document or empty dict if not found
        """
        logger.info(f"Getting document with ID: {document_id}")

        # Parse channel and period type from document ID
        try:
            # Format: {channel}-{period_type}-{period_key}-{hash}
            parts = document_id.split("-")
            if len(parts) >= 4:
                channel = parts[0]
                period_type = parts[1]

                # Pass time period to fetch_documents if it's in the ID
                time_period_kwargs = {}
                if period_type in ["monthly", "weekly", "daily"]:
                    time_period_kwargs["time_period"] = period_type
                elif period_type.startswith("custom"):
                    # Extract custom days from period type (format: custom-30day)
                    try:
                        custom_days = int(period_type.split("-")[1].replace("day", ""))
                        time_period_kwargs["time_period"] = "custom"
                        time_period_kwargs["custom_days"] = custom_days
                    except (IndexError, ValueError):
                        # If we can't parse custom days, use default
                        time_period_kwargs["time_period"] = "custom"
            else:
                # Can't parse channel from ID
                logger.error(f"Invalid document ID format: {document_id}")
                return {}
        except Exception as e:
            logger.error(f"Error parsing document ID: {str(e)}")
            return {}

        # Fetch all documents from the channel with the appropriate time period
        channel_docs = self.fetch_documents(channel_id=channel, **time_period_kwargs)

        # Find the document with the matching ID
        for doc in channel_docs:
            if doc.get("id") == document_id:
                return doc

        # If not found with the specific time period, try with default settings
        if time_period_kwargs:
            logger.info(f"Document not found with specific time period, trying with default settings")
            channel_docs = self.fetch_documents(channel_id=channel)

            for doc in channel_docs:
                if doc.get("id") == document_id:
                    return doc

        logger.warning(f"Document with ID {document_id} not found")
        return {}

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents matching the query.

        Args:
            query: Query to search for
            **kwargs: Additional arguments
                - limit: Maximum number of documents to return
                - channel_id: Optional channel ID to filter by
                - time_period: Time period to search in (monthly, weekly, daily, custom)
                - custom_days: Number of days for custom time period
                - quality_threshold: Minimum quality score for results
                - date_range: Optional tuple of (start_date, end_date) to filter by date

        Returns:
            List[Dict[str, Any]]: List of matching documents
        """
        logger.info(f"Searching for documents matching query: {query}")

        limit = kwargs.get("limit", 10)
        channel_id = kwargs.get("channel_id")
        time_period = kwargs.get("time_period", self.time_period)
        custom_days = kwargs.get("custom_days", self.custom_days)
        quality_threshold = kwargs.get("quality_threshold", self.quality_threshold)
        date_range = kwargs.get("date_range")

        # Fetch documents with time period configuration
        fetch_kwargs = {
            "channel_id": channel_id,
            "limit": 1000,  # Fetch more documents for better search results
            "time_period": time_period
        }

        if time_period == "custom" and custom_days:
            fetch_kwargs["custom_days"] = custom_days

        documents = self.fetch_documents(**fetch_kwargs)

        # Apply quality threshold
        if quality_threshold > 0:
            documents = [
                doc for doc in documents
                if doc.get("metadata", {}).get("quality_score", 0) >= quality_threshold
            ]

        # Apply date range filter if provided
        if date_range and len(date_range) == 2:
            start_date, end_date = date_range
            filtered_docs = []

            for doc in documents:
                doc_start = doc.get("metadata", {}).get("start_time", "")
                doc_end = doc.get("metadata", {}).get("end_time", "")

                # Skip documents without valid dates
                if not doc_start or not doc_end:
                    continue

                try:
                    # Parse ISO format dates
                    doc_start_date = datetime.fromisoformat(doc_start)
                    doc_end_date = datetime.fromisoformat(doc_end)

                    # Check if document overlaps with date range
                    if (not start_date or doc_end_date >= start_date) and \
                       (not end_date or doc_start_date <= end_date):
                        filtered_docs.append(doc)
                except (ValueError, TypeError):
                    # Skip documents with invalid date formats
                    continue

            documents = filtered_docs

        # Enhanced search implementation with better relevance scoring
        query_lower = query.lower()
        query_terms = query_lower.split()

        # Score documents based on multiple factors
        scored_docs = []
        for doc in documents:
            content = doc.get("content", "").lower()
            title = doc.get("title", "").lower()
            metadata = doc.get("metadata", {})

            # Calculate match score with multiple components
            score = 0

            # 1. Exact phrase match (highest weight)
            if query_lower in content:
                score += 10
            if query_lower in title:
                score += 5

            # 2. Term matches with position weighting
            # Terms appearing earlier in content get higher weight
            for term in query_terms:
                if term in content:
                    # Basic term match
                    score += 1

                    # Position boost - terms appearing in first 20% of content get extra weight
                    term_pos = content.find(term)
                    if term_pos > 0 and term_pos < len(content) * 0.2:
                        score += 0.5

                if term in title:
                    score += 0.5

            # 3. Technical term matches
            tech_terms = metadata.get("technical_terms", [])
            for term in query_terms:
                if term in tech_terms:
                    score += 2

            # 4. Content type and feature boosts
            # Code block bonus
            if "code" in query_lower and metadata.get("has_code", False):
                score += 3

            # Thread count bonus - documents with more threads are likely more comprehensive
            thread_count = metadata.get("thread_count", 0)
            if thread_count > 5:
                score += min(2, thread_count * 0.1)

            # Message count bonus - more messages indicate more comprehensive content
            message_count = metadata.get("message_count", 0)
            if message_count > 10:
                score += min(1.5, message_count * 0.05)

            # 5. Apply quality score as a multiplier
            quality_score = metadata.get("quality_score", 0.5)
            score *= (1 + quality_score)

            # 6. Time period relevance
            period_type = metadata.get("period_type", "")

            # Boost documents with matching time period type
            if period_type == time_period:
                score *= 1.2

            # 7. Cross-reference boost
            if self.enable_semantic_cross_refs:
                cross_refs = metadata.get("cross_references", [])
                if cross_refs:
                    # Boost based on number and quality of cross-references
                    cross_ref_boost = min(0.3, len(cross_refs) * 0.05)

                    # Additional boost if cross-references are relevant to the query
                    for ref in cross_refs:
                        ref_title = ref.get("title", "").lower()
                        if query_lower in ref_title:
                            cross_ref_boost += 0.1
                            break

                    score *= (1 + cross_ref_boost)

            # Add search boost to metadata
            doc_copy = doc.copy()
            if "metadata" not in doc_copy:
                doc_copy["metadata"] = {}
            doc_copy["metadata"]["search_boost"] = score
            doc_copy["metadata"]["search_query"] = query

            # Only include documents with a positive score
            if score > 0:
                scored_docs.append((score, doc_copy))

        # Sort by score (descending)
        scored_docs.sort(key=lambda x: x[0], reverse=True)

        # Return top results
        return [doc for _, doc in scored_docs[:limit]]

    def get_pagination_cursor(self) -> Optional[int]:
        """
        Get the cursor for the next page of results.

        Returns:
            Optional[int]: Cursor for the next page or None if no more pages
        """
        return self._next_cursor

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "local_slack"

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        # Check if data directory exists
        if not os.path.exists(self.data_dir):
            logger.error(f"Data directory {self.data_dir} does not exist")
            return False

        # Check if there are any JSON files in the data directory
        json_files = [f for f in os.listdir(self.data_dir) if f.endswith(".json")]
        if not json_files:
            logger.error(f"No JSON files found in {self.data_dir}")
            return False

        # Check for users.json (warning only)
        if "users.json" not in json_files:
            logger.warning(f"users.json not found in {self.data_dir}. User resolution will be limited.")

        return True

    def _compute_semantic_similarity(self, text1: str, text2: str) -> float:
        """
        Compute semantic similarity between two texts using embeddings with caching.

        This is a production-ready implementation that uses embeddings from a language model
        for accurate semantic similarity calculation, with fallback to Jaccard similarity.
        It includes caching for better performance with repeated comparisons.

        Args:
            text1: First text
            text2: Second text

        Returns:
            float: Similarity score between 0 and 1
        """
        # Skip empty texts
        if not text1 or not text2:
            return 0.0

        # Skip if embeddings are disabled
        if not self.use_embeddings:
            return self._compute_jaccard_similarity(text1, text2)

        # Try to use embeddings for similarity calculation
        try:
            # Get embeddings with caching
            embedding1 = self._get_cached_embedding(text1)
            embedding2 = self._get_cached_embedding(text2)

            # Calculate cosine similarity using numpy
            import numpy as np

            # Normalize vectors to unit length
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)

            if norm1 > 0 and norm2 > 0:
                # Compute cosine similarity
                cosine_similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)

                # Ensure the similarity is between 0 and 1
                similarity = max(0.0, min(1.0, cosine_similarity))

                logger.debug(f"Embedding similarity: {similarity:.4f}")
                return similarity
            else:
                logger.warning("Zero-length embedding detected, falling back to lexical similarity")
        except Exception as e:
            logger.warning(f"Error computing embedding similarity: {str(e)}. Falling back to lexical similarity.")

        # Fallback to Jaccard similarity if embeddings fail
        return self._compute_jaccard_similarity(text1, text2)

    def _get_cached_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text with caching for better performance.

        Args:
            text: Text to embed

        Returns:
            List[float]: Embedding vector
        """
        # Use a hash of the text as the cache key
        import hashlib
        cache_key = hashlib.md5(text.encode()).hexdigest()

        # Check if embedding is in cache
        if cache_key in self._embedding_cache:
            return self._embedding_cache[cache_key]

        # If not, generate embedding
        try:
            # Import here to avoid circular imports
            from apps.core.utils.domain_embeddings import get_domain_embedding_model

            # Initialize embedding model if not already done
            if self._embedding_model is None:
                self._embedding_model = get_domain_embedding_model(
                    content_type=self.embedding_content_type,
                    model_name=self.embedding_model_name
                )

            # Generate embedding
            embedding = self._embedding_model.embed_query(text)

            # Cache the embedding
            # Implement simple LRU cache by removing oldest entries if cache is full
            if len(self._embedding_cache) >= self.embedding_cache_size:
                # Remove a random entry (simple approach)
                # In a more sophisticated implementation, use collections.OrderedDict for true LRU
                if self._embedding_cache:
                    self._embedding_cache.pop(next(iter(self._embedding_cache)))

            # Add to cache
            self._embedding_cache[cache_key] = embedding

            return embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            # Return empty embedding as fallback
            return []

    def _compute_jaccard_similarity(self, text1: str, text2: str) -> float:
        """
        Compute Jaccard similarity between two texts with technical term weighting.

        Args:
            text1: First text
            text2: Second text

        Returns:
            float: Similarity score between 0 and 1
        """
        # Tokenize and normalize
        def tokenize(text):
            # Convert to lowercase and split by non-alphanumeric characters
            return set(re.findall(r'\w+', text.lower()))

        # Get unique tokens for each text
        tokens1 = tokenize(text1)
        tokens2 = tokenize(text2)

        # Skip if either text has no tokens
        if not tokens1 or not tokens2:
            return 0.0

        # Calculate Jaccard similarity (intersection over union)
        intersection = tokens1.intersection(tokens2)
        union = tokens1.union(tokens2)

        # Calculate similarity
        jaccard_similarity = len(intersection) / len(union) if union else 0.0

        # Calculate weighted similarity based on important terms
        important_terms = {
            "rag", "semantic", "cross", "reference", "vector", "database",
            "similarity", "search", "document", "embedding", "chunking",
            "quality", "thread", "window", "topic", "meeting", "issue",
            "performance", "code", "implementation"
        }

        # Count important terms in intersection
        important_intersection = intersection.intersection(important_terms)
        important_weight = len(important_intersection) * 0.05

        # Combine Jaccard similarity with important term weight
        similarity = min(1.0, jaccard_similarity + important_weight)

        # Debug output
        if similarity > 0.05:
            logger.debug(f"Jaccard similarity: {similarity:.4f}, Base: {jaccard_similarity:.4f}, Important: {important_weight:.4f}")
            logger.debug(f"Intersection: {intersection}")

        return similarity

    def _find_cross_references(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Find semantic cross-references between documents with optimized performance.

        This implementation uses batch processing and time-based filtering to reduce
        the computational complexity from O(n²) to a more manageable level.

        Args:
            documents: List of documents to find cross-references for

        Returns:
            Dict[str, List[Dict[str, Any]]]: Dictionary mapping document IDs to lists of cross-references
        """
        if not self.enable_semantic_cross_refs:
            return {}

        logger.info("Finding semantic cross-references between documents...")
        start_time = datetime.now()

        # Cache documents by ID for quick lookup
        for doc in documents:
            self._document_cache[doc["id"]] = doc

        # Initialize cross-references dictionary
        cross_references = defaultdict(list)

        # Group documents by time to reduce comparison space
        time_grouped_docs = self._group_documents_by_time(documents)

        # Track progress
        total_comparisons = 0
        completed_comparisons = 0
        doc_count = len(documents)

        # Calculate total comparisons for progress tracking
        for time_group, docs in time_grouped_docs.items():
            group_size = len(docs)
            total_comparisons += group_size * (group_size - 1) // 2

        logger.info(f"Processing {doc_count} documents with {total_comparisons} potential comparisons")

        # Process each time group
        for time_group, docs in time_grouped_docs.items():
            # Use batch processing for better performance
            self._process_document_batch(docs, cross_references, completed_comparisons, total_comparisons)
            completed_comparisons += len(docs) * (len(docs) - 1) // 2

        # Sort and limit cross-references
        for doc_id in cross_references:
            cross_references[doc_id].sort(key=lambda x: x["similarity"], reverse=True)
            cross_references[doc_id] = cross_references[doc_id][:self.cross_ref_max_references]

        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        logger.info(f"Found cross-references for {len(cross_references)} documents in {processing_time:.2f} seconds")

        return cross_references

    def _group_documents_by_time(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Group documents by time periods to reduce the comparison space.

        Args:
            documents: List of documents to group

        Returns:
            Dict[str, List[Dict[str, Any]]]: Dictionary mapping time periods to lists of documents
        """
        # Group documents by time periods (e.g., days or weeks)
        time_groups = defaultdict(list)

        for doc in documents:
            try:
                # Get document time
                doc_time = datetime.fromisoformat(doc["metadata"]["start_time"])

                # Create a time group key (e.g., YYYY-MM-DD for daily grouping)
                # Using a 3-day window for grouping to ensure overlap
                group_key = doc_time.strftime("%Y-%m-%d")

                # Add document to its time group
                time_groups[group_key].append(doc)

                # Also add to adjacent time groups to ensure we don't miss cross-references
                # at the boundaries of time periods
                prev_day = (doc_time - timedelta(days=1)).strftime("%Y-%m-%d")
                next_day = (doc_time + timedelta(days=1)).strftime("%Y-%m-%d")

                # Create empty lists for adjacent days if they don't exist
                if prev_day not in time_groups:
                    time_groups[prev_day] = []
                if next_day not in time_groups:
                    time_groups[next_day] = []

            except (ValueError, KeyError) as e:
                logger.warning(f"Error grouping document by time: {str(e)}")
                # Add to a default group if there's an error
                time_groups["default"].append(doc)

        return time_groups

    def _process_document_batch(self, documents: List[Dict[str, Any]],
                               cross_references: Dict[str, List[Dict[str, Any]]],
                               completed: int, total: int) -> None:
        """
        Process a batch of documents to find cross-references with optimized batch processing.

        This implementation uses batch embedding processing for better performance
        and includes progress tracking and time-based filtering.

        Args:
            documents: List of documents to process
            cross_references: Dictionary to store cross-references
            completed: Number of comparisons completed so far
            total: Total number of comparisons to process
        """
        # Skip if batch is too small
        if len(documents) < 2:
            return

        # Pre-compute document times for filtering
        doc_times = {}
        for doc in documents:
            try:
                doc_times[doc["id"]] = datetime.fromisoformat(doc["metadata"]["start_time"])
            except (ValueError, KeyError) as e:
                logger.warning(f"Error parsing document time: {str(e)}")
                # Use current time as fallback
                doc_times[doc["id"]] = datetime.now()

        # Pre-cache embeddings for all documents in the batch if using embeddings
        if self.use_embeddings:
            self._precache_embeddings([doc["content"] for doc in documents])

        # Process each document pair in the batch
        for i, doc1 in enumerate(documents):
            doc_id = doc1["id"]
            doc_content = doc1["content"]
            doc_time = doc_times.get(doc_id)

            # Log progress periodically
            if i % self.progress_logging_interval == 0:
                progress = (completed + i) / total * 100 if total > 0 else 0
                logger.debug(f"Cross-reference progress: {progress:.1f}% ({completed + i}/{total})")

            # Compare with other documents in the batch
            for j in range(i + 1, len(documents)):
                doc2 = documents[j]
                doc2_id = doc2["id"]

                # Skip if documents are too far apart in time
                doc2_time = doc_times.get(doc2_id)
                time_diff = abs((doc_time - doc2_time).total_seconds())
                if time_diff > (self.cross_ref_time_window_days * 24 * 60 * 60):
                    continue

                # Compute similarity
                similarity = self._compute_semantic_similarity(doc_content, doc2["content"])

                # Add as cross-reference if similarity is high enough
                if similarity >= self.cross_ref_min_similarity:
                    # Add doc2 as reference for doc1
                    cross_references[doc_id].append({
                        "document_id": doc2_id,
                        "similarity": similarity,
                        "title": doc2["title"],
                        "chunking_strategy": doc2["metadata"]["chunking_strategy"],
                        "time_difference_seconds": time_diff
                    })

                    # Add doc1 as reference for doc2 (bidirectional) if enabled
                    if self.bidirectional_references:
                        cross_references[doc2_id].append({
                            "document_id": doc_id,
                            "similarity": similarity,
                            "title": doc1["title"],
                            "chunking_strategy": doc1["metadata"]["chunking_strategy"],
                            "time_difference_seconds": time_diff
                        })

    def _precache_embeddings(self, texts: List[str]) -> None:
        """
        Pre-cache embeddings for a batch of texts to improve performance.

        Args:
            texts: List of texts to embed
        """
        if not self.use_embeddings or not texts:
            return

        try:
            # Import here to avoid circular imports
            from apps.core.utils.domain_embeddings import get_domain_embedding_model

            # Initialize embedding model if not already done
            if self._embedding_model is None:
                self._embedding_model = get_domain_embedding_model(
                    content_type=self.embedding_content_type,
                    model_name=self.embedding_model_name
                )

            # Create cache keys for all texts
            import hashlib
            cache_keys = [hashlib.md5(text.encode()).hexdigest() for text in texts]

            # Filter out texts that are already cached
            uncached_texts = []
            uncached_indices = []

            for i, (text, key) in enumerate(zip(texts, cache_keys)):
                if key not in self._embedding_cache:
                    uncached_texts.append(text)
                    uncached_indices.append(i)

            # Skip if all texts are already cached
            if not uncached_texts:
                return

            # Generate embeddings in batches
            batch_size = self.embedding_batch_size
            for i in range(0, len(uncached_texts), batch_size):
                batch_texts = uncached_texts[i:i+batch_size]
                batch_indices = uncached_indices[i:i+batch_size]

                # Generate embeddings for the batch
                try:
                    batch_embeddings = self._embedding_model.embed_documents(batch_texts)

                    # Cache the embeddings
                    for j, embedding in enumerate(batch_embeddings):
                        text_index = batch_indices[j]
                        key = cache_keys[text_index]

                        # Manage cache size
                        if len(self._embedding_cache) >= self.embedding_cache_size:
                            # Remove a random entry (simple approach)
                            if self._embedding_cache:
                                self._embedding_cache.pop(next(iter(self._embedding_cache)))

                        # Add to cache
                        self._embedding_cache[key] = embedding

                except Exception as e:
                    logger.error(f"Error generating batch embeddings: {str(e)}")

        except Exception as e:
            logger.error(f"Error in batch embedding pre-caching: {str(e)}")