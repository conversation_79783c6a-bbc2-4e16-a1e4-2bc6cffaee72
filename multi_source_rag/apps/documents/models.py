# Import TenantAwareModel directly since it's an abstract model
from apps.core.models import TenantAwareModel
from django.contrib.auth.models import User
from django.db import models


class DocumentSource(TenantAwareModel):
    """
    Model to track document sources (e.g., Slack, GitHub, etc.).
    """

    name = models.CharField(max_length=100)
    source_type = models.CharField(
        max_length=20,
        choices=[
            ("slack", "Slack"),
            ("local_slack", "Local Slack"),
            ("github", "GitHub"),
            ("confluence", "Confluence"),
            ("file", "File Upload"),
            ("web", "Web Scrape"),
            ("other", "Other"),
        ],
    )
    config = models.JSONField(
        default=dict, blank=True, help_text="Source-specific configuration"
    )
    credentials = models.JSONField(
        default=dict, blank=True, help_text="Credentials for the source (encrypted)"
    )
    last_synced = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.source_type})"


class RawDocument(TenantAwareModel):
    """
    The core document entity representing individual content pieces.
    Contains metadata and permalinks essential for citations.
    """

    title = models.CharField(max_length=255)
    source = models.ForeignKey(
        DocumentSource, on_delete=models.CASCADE, related_name="documents"
    )
    external_id = models.CharField(max_length=255, blank=True, null=True)
    permalink = models.URLField(
        blank=True, null=True, help_text="Permanent link to the original document"
    )
    fetched_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)
    content_hash = models.CharField(max_length=64, blank=True, null=True)
    content_type = models.CharField(
        max_length=20,
        choices=[
            ("text", "Plain Text"),
            ("markdown", "Markdown"),
            ("html", "HTML"),
            ("pdf", "PDF"),
            ("code", "Code"),
            ("github_pr", "GitHub PR"),
            ("github_issue", "GitHub Issue"),
            ("slack_message", "Slack Message"),
            ("other", "Other"),
        ],
        default="text",
    )
    quality_score = models.FloatField(
        null=True, blank=True, help_text="Quality score of the document content"
    )
    is_high_quality = models.BooleanField(
        default=False, help_text="Whether the document is considered high quality"
    )
    technical_entities = models.JSONField(
        default=dict,
        blank=True,
        help_text="Technical entities extracted from the document",
    )

    class Meta:
        verbose_name = "Document"
        verbose_name_plural = "Documents"
        app_label = "documents"

    def __str__(self):
        return self.title


class DocumentChunk(TenantAwareModel):
    """
    Represents chunks of text created from documents during the processing pipeline.
    Links to both the source document and the authoring platform profile for complete citation tracking.
    """

    document = models.ForeignKey(
        RawDocument, on_delete=models.CASCADE, related_name="chunks"
    )
    profile = models.ForeignKey(
        "accounts.UserPlatformProfile",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="authored_chunks",
        help_text="Author's platform profile",
    )
    text = models.TextField()
    chunk_type = models.CharField(max_length=50, default="text")
    thread_id = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    token_count = models.IntegerField(default=0)
    importance_score = models.FloatField(default=0.0)
    quality_score = models.FloatField(
        null=True, blank=True, help_text="Quality score of the chunk content"
    )
    is_high_quality = models.BooleanField(
        default=False, help_text="Whether the chunk is considered high quality"
    )
    chunk_index = models.IntegerField()
    metadata = models.JSONField(default=dict, blank=True)
    technical_entities = models.JSONField(
        default=dict,
        blank=True,
        help_text="Technical entities extracted from the chunk",
    )

    class Meta:
        unique_together = ("document", "chunk_index")
        ordering = ["document", "chunk_index"]
        app_label = "documents"

    def __str__(self):
        return f"{self.document.title} - Chunk {self.chunk_index}"


class EmbeddingMetadata(models.Model):
    """
    Stores vector embeddings metadata generated for document chunks.
    The separation of embedding metadata from content enables vector store flexibility.

    This model serves as a critical mapping between PostgreSQL integer IDs and
    vector database UUID strings, ensuring consistent ID handling across the system.
    """

    class Meta:
        app_label = "documents"
        indexes = [
            models.Index(fields=["vector_id"], name="vector_id_idx"),
        ]

    chunk = models.OneToOneField(
        DocumentChunk, on_delete=models.CASCADE, related_name="embedding"
    )
    vector_id = models.CharField(
        max_length=255, db_index=True, help_text="UUID used in vector database"
    )
    embedded_at = models.DateTimeField(auto_now_add=True)
    model_name = models.CharField(max_length=100)
    vector_dimensions = models.IntegerField()
    is_synced = models.BooleanField(
        default=True,
        help_text="Whether the embedding is synced with the vector database",
    )

    def __str__(self):
        return f"Embedding for {self.chunk}"

    @classmethod
    def get_chunk_by_vector_id(cls, vector_id):
        """
        Get a document chunk by its vector ID.

        Args:
            vector_id: Vector ID to look up

        Returns:
            DocumentChunk or None: The document chunk if found, None otherwise
        """
        try:
            embedding = cls.objects.filter(vector_id=vector_id).first()
            if embedding:
                return embedding.chunk
            return None
        except Exception as e:
            import logging

            logging.getLogger(__name__).error(
                f"Error getting chunk by vector ID: {str(e)}"
            )
            return None


class RelatedChunk(models.Model):
    """
    Tracks relationships between chunks, such as thread replies in Slack or related sections in documentation.
    Enables preservation of conversational context.

    Note: This model is deprecated and will be replaced by the ChunkRelationship model in the future.
    Use ChunkRelationship for new code.
    """

    chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="related_chunks"
    )
    related_chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="related_to"
    )
    relation_type = models.CharField(max_length=50)
    relation_strength = models.FloatField(default=0.0)
    metadata = models.JSONField(
        default=dict, blank=True, help_text="Additional metadata about the relationship"
    )

    class Meta:
        unique_together = ("chunk", "related_chunk")
        app_label = "documents"

    def __str__(self):
        return f"{self.chunk} -> {self.related_chunk} ({self.relation_type})"


class ChunkRelationship(TenantAwareModel):
    """
    Tracks relationships between chunks with more detailed metadata.
    Enables tracking of parent-child, previous-next, and other relationships.
    """

    source_chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="outgoing_relationships"
    )
    target_chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="incoming_relationships"
    )
    relationship_type = models.CharField(
        max_length=50,
        help_text="Type of relationship (parent, child, previous, next, etc.)",
    )
    strength = models.FloatField(
        default=1.0, help_text="Strength of the relationship (0-1)"
    )
    metadata = models.JSONField(
        default=dict, blank=True, help_text="Additional metadata about the relationship"
    )

    class Meta:
        unique_together = ("source_chunk", "target_chunk", "relationship_type")
        app_label = "documents"

    def __str__(self):
        return f"{self.source_chunk} -> {self.target_chunk} ({self.relationship_type})"


class CrossPlatformReference(TenantAwareModel):
    """
    Tracks references between different platforms (Slack, GitHub, Jira, etc.).
    Enables cross-platform context preservation.
    """

    source_chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="outgoing_references"
    )
    target_chunk = models.ForeignKey(
        DocumentChunk,
        on_delete=models.CASCADE,
        related_name="incoming_references",
        null=True,
        blank=True,
    )
    reference_type = models.CharField(
        max_length=50, help_text="Type of reference (github, slack, jira, etc.)"
    )
    reference_id = models.CharField(
        max_length=255, help_text="ID of the referenced item"
    )
    url = models.URLField(blank=True, null=True, help_text="URL to the referenced item")
    context = models.TextField(
        blank=True, null=True, help_text="Context of the reference"
    )
    metadata = models.JSONField(
        default=dict, blank=True, help_text="Additional metadata about the reference"
    )
    confidence = models.FloatField(
        default=1.0, help_text="Confidence score of the reference (0-1)"
    )

    def __str__(self):
        if self.target_chunk:
            return f"{self.source_chunk} -> {self.target_chunk} ({self.reference_type})"
        return f"{self.source_chunk} -> {self.reference_type}:{self.reference_id}"


class DocumentContentManager(models.Manager):
    """Custom manager for DocumentContent with performance optimizations."""

    def get_content_preview_only(self):
        """Get queryset that only loads preview fields, not full content."""
        return self.only('id', 'document_id', 'content_format', 'content_size', 'content_summary')

    def get_small_content_only(self, max_size: int = 5000):
        """Get queryset for documents with content smaller than max_size."""
        return self.filter(content_size__lte=max_size)

    def get_large_content_only(self, min_size: int = 10000):
        """Get queryset for documents with content larger than min_size."""
        return self.filter(content_size__gte=min_size)

    def bulk_update_summaries(self, batch_size: int = 100):
        """Bulk update summaries for documents that don't have them."""
        documents_without_summary = self.filter(
            content_summary__isnull=True,
            content_size__gt=1000
        )

        updated_count = 0
        for doc_content in documents_without_summary.iterator(chunk_size=batch_size):
            if not doc_content.content_summary:
                doc_content.content_summary = doc_content._generate_summary()
                doc_content.save(update_fields=['content_summary'])
                updated_count += 1

        return updated_count


class DocumentContent(models.Model):
    """
    Model to store document content separately from metadata.
    This allows for storing large document content without affecting query performance.
    """

    document = models.OneToOneField(
        RawDocument, on_delete=models.CASCADE, related_name="document_content"
    )
    content = models.TextField(help_text="The full content of the document")
    content_format = models.CharField(
        max_length=20,
        choices=[
            ("text", "Plain Text"),
            ("markdown", "Markdown"),
            ("html", "HTML"),
            ("slack", "Slack Format"),
        ],
        default="text",
    )
    content_size = models.IntegerField(
        default=0,
        help_text="Size of content in characters for optimization"
    )
    content_summary = models.TextField(
        blank=True,
        null=True,
        help_text="Brief summary of content for quick access"
    )

    # Custom manager
    objects = DocumentContentManager()

    class Meta:
        app_label = "documents"

    def __str__(self):
        return f"Content for {self.document}"

    def save(self, *args, **kwargs):
        """Override save to calculate content size."""
        if self.content:
            self.content_size = len(self.content)

            # Generate summary for large content if not provided
            if not self.content_summary and self.content_size > 1000:
                self.content_summary = self._generate_summary()

        super().save(*args, **kwargs)

    def _generate_summary(self) -> str:
        """Generate a brief summary of the content."""
        if not self.content:
            return ""

        # Simple summary: first 200 characters + last 100 characters
        content_clean = self.content.strip()
        if len(content_clean) <= 300:
            return content_clean

        summary = content_clean[:200] + "..." + content_clean[-100:]
        return summary

    def get_content_preview(self, max_length: int = 500) -> str:
        """
        Get a preview of the content without loading the full content.

        Args:
            max_length: Maximum length of preview

        Returns:
            str: Content preview
        """
        if self.content_summary and len(self.content_summary) <= max_length:
            return self.content_summary

        if self.content_size <= max_length:
            return self.content

        # Return truncated content
        return self.content[:max_length] + "..." if self.content else ""

    def is_large_content(self, threshold: int = 10000) -> bool:
        """
        Check if content is considered large.

        Args:
            threshold: Size threshold in characters

        Returns:
            bool: True if content is large
        """
        return self.content_size > threshold


class DocumentProcessingJob(TenantAwareModel):
    """
    Model to track document processing jobs.
    """

    source = models.ForeignKey(
        DocumentSource, on_delete=models.CASCADE, related_name="jobs"
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ("pending", "Pending"),
            ("processing", "Processing"),
            ("completed", "Completed"),
            ("failed", "Failed"),
        ],
        default="pending",
    )
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="processing_jobs"
    )
    documents_processed = models.IntegerField(default=0)
    documents_failed = models.IntegerField(default=0)

    def __str__(self):
        return f"Job {self.id} - {self.source.name} ({self.status})"
