#!/usr/bin/env python
import os, sys, django
sys.path.append('.'); os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local"); django.setup()
from apps.documents.services.ingestion_service import IngestionService
from apps.accounts.models import Tenant

tenant, _ = Tenant.objects.get_or_create(slug="default", defaults={"name": "Default"})
service = IngestionService(tenant=tenant)
source = service.create_source("Local Slack", "local_slack", {"data_dir": "../data/", "channel": "C065QSSNH8A"})
processed, failed = service.process_source(source, batch_size=50)
print(f"✅ Processed: {processed}, Failed: {failed}")
